<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YendorCats Admin - Client & Appointment Manager</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 20px;
        }
        .tab-content {
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .card {
            margin-bottom: 20px;
        }
        .hidden {
            display: none;
        }
        .cursor-pointer {
            cursor: pointer;
        }
        .appointment-card {
            transition: all 0.3s ease;
        }
        .appointment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .badge-scheduled { background-color: #17a2b8; }
        .badge-confirmed { background-color: #28a745; }
        .badge-completed { background-color: #6c757d; }
        .badge-cancelled { background-color: #dc3545; }
        .badge-rescheduled { background-color: #ffc107; color: #212529; }
        .badge-noshow { background-color: #6610f2; }
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 0.2em solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border .75s linear infinite;
        }
        @keyframes spinner-border {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="pb-3 mb-4 border-bottom">
            <div class="d-flex align-items-center justify-content-between">
                <h1 class="h3">YendorCats Admin - Client & Appointment Manager</h1>
                <div>
                    <span id="loggedInUser" class="me-2"></span>
                    <button id="logoutBtn" class="btn btn-outline-danger btn-sm">Logout</button>
                </div>
            </div>
        </header>
        
        <!-- Login Form -->
        <div id="loginSection" class="card shadow-sm mb-4">
            <div class="card-body">
                <h5 class="card-title">Admin Login</h5>
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Login</button>
                    <div id="loginMessage" class="mt-3 text-danger"></div>
                </form>
            </div>
        </div>

        <!-- Main Content (Hidden until logged in) -->
        <div id="mainContent" class="hidden">
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="clients-tab" data-bs-toggle="tab" data-bs-target="#clients" type="button" role="tab" aria-controls="clients" aria-selected="true">Clients</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="appointments-tab" data-bs-toggle="tab" data-bs-target="#appointments" type="button" role="tab" aria-controls="appointments" aria-selected="false">Appointments</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="calendar-tab" data-bs-toggle="tab" data-bs-target="#calendar" type="button" role="tab" aria-controls="calendar" aria-selected="false">Calendar</button>
                </li>
            </ul>
            <div class="tab-content" id="myTabContent">
                <!-- Clients Tab -->
                <div class="tab-pane fade show active" id="clients" role="tabpanel" aria-labelledby="clients-tab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2 class="h4">Client Management</h2>
                        <div>
                            <div class="input-group">
                                <input type="text" id="clientSearch" class="form-control" placeholder="Search clients...">
                                <button class="btn btn-outline-secondary" type="button" id="searchClientBtn">Search</button>
                            </div>
                        </div>
                        <button id="addClientBtn" class="btn btn-primary">Add New Client</button>
                    </div>
                    
                    <div id="clientsList" class="row">
                        <p id="clientsLoading">Loading clients...</p>
                    </div>
                    
                    <!-- Client Modal -->
                    <div class="modal fade" id="clientModal" tabindex="-1" aria-labelledby="clientModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="clientModalLabel">Add/Edit Client</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="clientForm">
                                        <input type="hidden" id="clientId">
                                        <div class="mb-3">
                                            <label for="firstName" class="form-label">First Name</label>
                                            <input type="text" class="form-control" id="firstName" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="lastName" class="form-label">Last Name</label>
                                            <input type="text" class="form-control" id="lastName" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="email" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">Phone</label>
                                            <input type="tel" class="form-control" id="phone">
                                        </div>
                                        <div class="mb-3">
                                            <label for="address" class="form-label">Address</label>
                                            <input type="text" class="form-control" id="address">
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="city" class="form-label">City</label>
                                                <input type="text" class="form-control" id="city">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="state" class="form-label">State</label>
                                                <input type="text" class="form-control" id="state">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="postalCode" class="form-label">Postal Code</label>
                                                <input type="text" class="form-control" id="postalCode">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="country" class="form-label">Country</label>
                                                <input type="text" class="form-control" id="country">
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">Notes</label>
                                            <textarea class="form-control" id="notes" rows="3"></textarea>
                                        </div>
                                        <div class="mb-3 form-check">
                                            <input type="checkbox" class="form-check-input" id="isActive" checked>
                                            <label class="form-check-label" for="isActive">Active Client</label>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary" id="saveClientBtn">Save Client</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Client Details Modal -->
                    <div class="modal fade" id="clientDetailsModal" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Client Details</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <h6>Contact Information</h6>
                                            <p id="detailName">Name: </p>
                                            <p id="detailEmail">Email: </p>
                                            <p id="detailPhone">Phone: </p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Address</h6>
                                            <p id="detailAddress"></p>
                                            <p id="detailCityState"></p>
                                            <p id="detailCountry"></p>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <h6>Notes</h6>
                                        <p id="detailNotes"></p>
                                    </div>
                                    <div class="mb-3">
                                        <h6>Client Since</h6>
                                        <p id="detailCreatedAt"></p>
                                    </div>
                                    <hr>
                                    <h5>Client Appointments</h5>
                                    <div id="clientAppointments">
                                        <p>Loading appointments...</p>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary" id="editClientFromDetailsBtn">Edit Client</button>
                                    <button type="button" class="btn btn-success" id="newAppointmentForClientBtn">New Appointment</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Appointments Tab -->
                <div class="tab-pane fade" id="appointments" role="tabpanel" aria-labelledby="appointments-tab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2 class="h4">Appointment Management</h2>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="showCompletedAppointments" checked>
                            <label class="form-check-label" for="showCompletedAppointments">Show Completed</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="showCancelledAppointments">
                            <label class="form-check-label" for="showCancelledAppointments">Show Cancelled</label>
                        </div>
                        <button id="addAppointmentBtn" class="btn btn-primary">Add New Appointment</button>
                    </div>
                    
                    <div id="appointmentsList" class="row">
                        <p id="appointmentsLoading">Loading appointments...</p>
                    </div>
                    
                    <!-- Appointment Modal -->
                    <div class="modal fade" id="appointmentModal" tabindex="-1" aria-labelledby="appointmentModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="appointmentModalLabel">Add/Edit Appointment</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="appointmentForm">
                                        <input type="hidden" id="appointmentId">
                                        <div class="mb-3">
                                            <label for="appointmentTitle" class="form-label">Title</label>
                                            <input type="text" class="form-control" id="appointmentTitle" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="clientSelect" class="form-label">Client</label>
                                            <select class="form-select" id="clientSelect" required>
                                                <option value="">Select a client</option>
                                            </select>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="startDate" class="form-label">Start Date</label>
                                                <input type="date" class="form-control" id="startDate" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="startTime" class="form-label">Start Time</label>
                                                <input type="time" class="form-control" id="startTime" required>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="endDate" class="form-label">End Date</label>
                                                <input type="date" class="form-control" id="endDate" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="endTime" class="form-label">End Time</label>
                                                <input type="time" class="form-control" id="endTime" required>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="location" class="form-label">Location</label>
                                            <input type="text" class="form-control" id="location">
                                        </div>
                                        <div class="mb-3" id="statusDiv">
                                            <label for="status" class="form-label">Status</label>
                                            <select class="form-select" id="status">
                                                <option value="Scheduled">Scheduled</option>
                                                <option value="Confirmed">Confirmed</option>
                                                <option value="Completed">Completed</option>
                                                <option value="Cancelled">Cancelled</option>
                                                <option value="Rescheduled">Rescheduled</option>
                                                <option value="NoShow">No Show</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="appointmentNotes" class="form-label">Notes</label>
                                            <textarea class="form-control" id="appointmentNotes" rows="3"></textarea>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary" id="saveAppointmentBtn">Save Appointment</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Appointment Details Modal -->
                    <div class="modal fade" id="appointmentDetailsModal" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Appointment Details</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <h6 id="appointmentDetailTitle" class="mb-3"></h6>
                                    <p id="appointmentDetailClient"><strong>Client:</strong> </p>
                                    <p id="appointmentDetailDate"><strong>Date/Time:</strong> </p>
                                    <p id="appointmentDetailLocation"><strong>Location:</strong> </p>
                                    <p id="appointmentDetailStatus"><strong>Status:</strong> </p>
                                    <div class="mb-3">
                                        <h6>Notes</h6>
                                        <p id="appointmentDetailNotes"></p>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary" id="editAppointmentBtn">Edit</button>
                                    <button type="button" class="btn btn-success" id="completeAppointmentBtn">Mark as Completed</button>
                                    <button type="button" class="btn btn-danger" id="cancelAppointmentBtn">Cancel Appointment</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Calendar Tab -->
                <div class="tab-pane fade" id="calendar" role="tabpanel" aria-labelledby="calendar-tab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2 class="h4">Appointment Calendar</h2>
                        <div>
                            <button id="todayBtn" class="btn btn-outline-secondary">Today</button>
                            <button id="prevWeekBtn" class="btn btn-outline-secondary">◀</button>
                            <span id="currentWeek" class="mx-2">Current Week</span>
                            <button id="nextWeekBtn" class="btn btn-outline-secondary">▶</button>
                        </div>
                    </div>
                    
                    <div id="weekCalendar" class="mb-3">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr id="calendarHeader">
                                        <th style="width: 50px;">Time</th>
                                        <!-- Day headers will be inserted here -->
                                    </tr>
                                </thead>
                                <tbody id="calendarBody">
                                    <!-- Calendar rows will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Toast notifications -->
        <div class="toast-container position-fixed bottom-0 end-0 p-3">
            <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <strong class="me-auto" id="toastTitle">Notification</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body" id="toastMessage">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Constants and Configuration
        const API_BASE_URL = '/api';
        const CLIENT_ENDPOINT = `${API_BASE_URL}/AdminClient`;
        const APPOINTMENT_ENDPOINT = `${API_BASE_URL}/AdminAppointment`;
        const AUTH_ENDPOINT = `${API_BASE_URL}/AdminAuth`;
        
        // UI Elements
        const loginSection = document.getElementById('loginSection');
        const mainContent = document.getElementById('mainContent');
        const loginForm = document.getElementById('loginForm');
        const loginMessage = document.getElementById('loginMessage');
        const loggedInUser = document.getElementById('loggedInUser');
        const logoutBtn = document.getElementById('logoutBtn');
        
        // Client UI Elements
        const clientsList = document.getElementById('clientsList');
        const clientsLoading = document.getElementById('clientsLoading');
        const clientSearch = document.getElementById('clientSearch');
        const searchClientBtn = document.getElementById('searchClientBtn');
        const addClientBtn = document.getElementById('addClientBtn');
        const clientModal = new bootstrap.Modal(document.getElementById('clientModal'));
        const clientForm = document.getElementById('clientForm');
        const saveClientBtn = document.getElementById('saveClientBtn');
        const clientDetailsModal = new bootstrap.Modal(document.getElementById('clientDetailsModal'));
        const editClientFromDetailsBtn = document.getElementById('editClientFromDetailsBtn');
        const newAppointmentForClientBtn = document.getElementById('newAppointmentForClientBtn');
        
        // Appointment UI Elements
        const appointmentsList = document.getElementById('appointmentsList');
        const appointmentsLoading = document.getElementById('appointmentsLoading');
        const showCompletedAppointments = document.getElementById('showCompletedAppointments');
        const showCancelledAppointments = document.getElementById('showCancelledAppointments');
        const addAppointmentBtn = document.getElementById('addAppointmentBtn');
        const appointmentModal = new bootstrap.Modal(document.getElementById('appointmentModal'));
        const appointmentForm = document.getElementById('appointmentForm');
        const saveAppointmentBtn = document.getElementById('saveAppointmentBtn');
        const appointmentDetailsModal = new bootstrap.Modal(document.getElementById('appointmentDetailsModal'));
        const editAppointmentBtn = document.getElementById('editAppointmentBtn');
        const completeAppointmentBtn = document.getElementById('completeAppointmentBtn');
        const cancelAppointmentBtn = document.getElementById('cancelAppointmentBtn');
        const clientSelect = document.getElementById('clientSelect');
        
        // Calendar UI Elements
        const todayBtn = document.getElementById('todayBtn');
        const prevWeekBtn = document.getElementById('prevWeekBtn');
        const nextWeekBtn = document.getElementById('nextWeekBtn');
        const currentWeek = document.getElementById('currentWeek');
        const calendarHeader = document.getElementById('calendarHeader');
        const calendarBody = document.getElementById('calendarBody');
        
        // Toast notifications
        const toast = new bootstrap.Toast(document.getElementById('toast'));
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');
        
        // State
        let currentClient = null;
        let currentAppointment = null;
        let allClients = [];
        let allAppointments = [];
        let currentCalendarDate = new Date();
        
        // Helper Functions
        function showToast(title, message, variant = 'success') {
            toastTitle.textContent = title;
            toastMessage.textContent = message;
            document.getElementById('toast').className = `toast text-bg-${variant}`;
            toast.show();
        }
        
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                weekday: 'short', 
                year: 'numeric', 
                month: 'short', 
                day: 'numeric'
            });
        }
        
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                weekday: 'short', 
                year: 'numeric', 
                month: 'short', 
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        function formatTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        function getAuthToken() {
            return localStorage.getItem('adminToken');
        }
        
        function getAuthHeader() {
            const token = getAuthToken();
            return token ? { 'Authorization': `Bearer ${token}` } : {};
        }
        
        async function fetchWithAuth(url, options = {}) {
            const headers = {
                'Content-Type': 'application/json',
                ...getAuthHeader(),
                ...options.headers
            };
            
            const response = await fetch(url, {
                ...options,
                headers
            });
            
            if (response.status === 401) {
                // Unauthorized, clear token and redirect to login
                localStorage.removeItem('adminToken');
                showLoginForm();
                throw new Error('Unauthorized. Please login again.');
            }
            
            return response;
        }
        
        // Authentication Functions
        async function login(username, password) {
            try {
                const response = await fetch(`${AUTH_ENDPOINT}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.message || 'Login failed');
                }
                
                // Store the token and user info
                localStorage.setItem('adminToken', data.token);
                localStorage.setItem('adminUser', JSON.stringify(data.user));
                
                return data.user;
            } catch (error) {
                console.error('Login error:', error);
                throw error;
            }
        }
        
        function logout() {
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminUser');
            showLoginForm();
        }
        
        function checkAuthStatus() {
            const token = getAuthToken();
            const userJson = localStorage.getItem('adminUser');
            
            if (token && userJson) {
                try {
                    const user = JSON.parse(userJson);
                    loggedInUser.textContent = `${user.username} (${user.role})`;
                    showMainContent();
                    loadInitialData();
                    return true;
                } catch (e) {
                    console.error('Error parsing user JSON:', e);
                }
            }
            
            showLoginForm();
            return false;
        }
        
        function showLoginForm() {
            loginSection.classList.remove('hidden');
            mainContent.classList.add('hidden');
        }
        
        function showMainContent() {
            loginSection.classList.add('hidden');
            mainContent.classList.remove('hidden');
        }
        
        // Client Functions
        async function loadClients(searchTerm = '') {
            clientsLoading.textContent = 'Loading clients...';
            
            try {
                const url = searchTerm 
                    ? `${CLIENT_ENDPOINT}/search?searchTerm=${encodeURIComponent(searchTerm)}` 
                    : CLIENT_ENDPOINT;
                
                const response = await fetchWithAuth(url);
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.message || 'Failed to load clients');
                }
                
                allClients = data.clients;
                renderClientsList(data.clients);
                
                // Update client select dropdown for appointments
                populateClientSelect();
                
            } catch (error) {
                console.error('Error loading clients:', error);
                clientsLoading.textContent = `Error: ${error.message}`;
            }
        }
        
        function renderClientsList(clients) {
            if (!clients || clients.length === 0) {
                clientsList.innerHTML = '<div class="col-12"><p>No clients found.</p></div>';
                return;
            }
            
            clientsList.innerHTML = '';
            
            clients.forEach(client => {
                const card = document.createElement('div');
                card.className = 'col-md-4 mb-3';
                card.innerHTML = `
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">${client.fullName}</h5>
                            <p class="card-text">
                                <strong>Email:</strong> ${client.email}<br>
                                <strong>Phone:</strong> ${client.phone || 'N/A'}<br>
                                <strong>Appointments:</strong> ${client.appointmentCount}
                            </p>
                        </div>
                        <div class="card-footer bg-transparent">
                            <button class="btn btn-sm btn-outline-primary view-client" data-id="${client.id}">View</button>
                            <button class="btn btn-sm btn-outline-secondary edit-client" data-id="${client.id}">Edit</button>
                            ${client.isActive 
                                ? `<button class="btn btn-sm btn-outline-danger deactivate-client" data-id="${client.id}">Deactivate</button>` 
                                : `<span class="badge bg-secondary">Inactive</span>`
                            }
                        </div>
                    </div>
                `;
                clientsList.appendChild(card);
            });
            
            // Add event listeners to buttons
            document.querySelectorAll('.view-client').forEach(btn => {
