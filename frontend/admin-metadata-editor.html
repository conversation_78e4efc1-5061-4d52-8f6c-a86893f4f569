<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yendor Cats - Metadata Editor</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/main.css">
    <style>
        .metadata-editor {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .editor-tabs {
            display: flex;
            background: #f5f5f5;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
            margin-bottom: 0;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: #f5f5f5;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: white;
            color: #2c3e50;
        }

        .tab-button:hover {
            background: #e8e8e8;
        }

        .tab-content {
            background: white;
            border: 1px solid #ddd;
            border-radius: 0 0 8px 8px;
            padding: 30px;
            min-height: 600px;
        }

        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .cat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .cat-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .cat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .cat-header {
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }

        .cat-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .cat-info {
            color: #666;
            font-size: 0.9em;
        }

        .cat-photos {
            padding: 15px;
        }

        .photo-thumbnail {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.2s ease;
        }

        .photo-thumbnail:hover {
            border-color: #3498db;
        }

        .photo-thumbnail.selected {
            border-color: #e74c3c;
        }

        .bulk-actions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .bulk-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .search-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metadata-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .family-tree {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            border: 1px solid transparent;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-error {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }

        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .form-group label {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            outline: none;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .btn:active {
            transform: translateY(0);
        }

        .search-filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .search-filters .form-group {
            margin-bottom: 15px;
        }

        .photo-thumbnail {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .photo-thumbnail:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .photo-thumbnail.selected {
            border: 3px solid #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .form-group input.error,
        .form-group select.error,
        .form-group textarea.error {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .field-error {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1003;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .success-indicator {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            margin: 10px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .family-tree-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .tree-structure {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .generation {
            text-align: center;
        }

        .generation h4 {
            margin-bottom: 10px;
            color: #495057;
        }

        .cat-nodes {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .cat-node {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            min-width: 150px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .cat-node:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .cat-node.main-cat {
            border-color: #007bff;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .cat-node.parent {
            border-color: #28a745;
        }

        .cat-node.grandparent {
            border-color: #ffc107;
        }

        .cat-node.offspring {
            border-color: #17a2b8;
        }

        .more-indicator {
            background: #6c757d;
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin: auto;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .selected-count {
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="metadata-editor">
        <header style="text-align: center; margin-bottom: 30px;">
            <h1>Yendor Cats Metadata Management System</h1>
            <p>Professional pedigree and photo organization for Maine Coon breeding</p>
        </header>

        <div class="editor-tabs">
            <button class="tab-button active" data-tab="overview">Overview</button>
            <button class="tab-button" data-tab="cats">Cat Profiles</button>
            <button class="tab-button" data-tab="photos">Photo Management</button>
            <button class="tab-button" data-tab="bulk">Bulk Operations</button>
            <button class="tab-button" data-tab="pedigree">Pedigree Builder</button>
        </div>

        <div class="tab-content">
            <!-- Overview Tab -->
            <div class="tab-panel active" id="overview-panel">
                <h2>Cattery Overview</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="total-cats">-</div>
                        <div>Total Cats</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="total-photos">-</div>
                        <div>Total Photos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="unlinked-photos">-</div>
                        <div>Unlinked Photos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="available-kittens">-</div>
                        <div>Available Kittens</div>
                    </div>
                </div>

                <div class="bulk-actions">
                    <h3>Quick Actions</h3>
                    <div class="bulk-controls">
                        <button class="btn btn-primary" onclick="refreshData()">Refresh Data</button>
                        <button class="btn btn-secondary" onclick="exportMetadata()">Export Metadata</button>
                        <button class="btn btn-warning" onclick="showS3Config()">S3 Configuration</button>
                        <button class="btn btn-success" onclick="switchToTab('photos')">Manage Unlinked Photos</button>
                    </div>
                </div>

                <div id="recent-activity">
                    <h3>Recent Activity</h3>
                    <div id="activity-list">
                        <p>Loading recent metadata changes...</p>
                    </div>
                </div>
            </div>

            <!-- Cat Profiles Tab -->
            <div class="tab-panel" id="cats-panel">
                <h2>Cat Profiles Management</h2>
                
                <div class="search-filters">
                    <div class="form-group">
                        <label>Search by Name</label>
                        <input type="text" id="cat-search" placeholder="Enter cat name...">
                    </div>
                    <div class="form-group">
                        <label>Filter by Breed</label>
                        <select id="breed-filter">
                            <option value="">All Breeds</option>
                            <option value="Maine Coon">Maine Coon</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Filter by Bloodline</label>
                        <select id="bloodline-filter">
                            <option value="">All Bloodlines</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Breeding Status</label>
                        <select id="status-filter">
                            <option value="">All Status</option>
                            <option value="available-kitten">Available Kitten</option>
                            <option value="breeding-queen">Breeding Queen</option>
                            <option value="stud">Stud</option>
                            <option value="retired">Retired</option>
                        </select>
                    </div>
                </div>

                <div class="bulk-controls" style="margin-bottom: 20px;">
                    <button class="btn btn-primary" onclick="searchCats()">Search</button>
                    <button class="btn btn-secondary" onclick="clearFilters()">Clear Filters</button>
                    <button class="btn btn-success" onclick="createNewCatProfile()">New Cat Profile</button>
                </div>

                <div class="cat-grid" id="cat-profiles-grid">
                    <p>Loading cat profiles...</p>
                </div>
            </div>

            <!-- Photo Management Tab -->
            <div class="tab-panel" id="photos-panel">
                <h2>Photo Management</h2>
                
                <div class="bulk-actions">
                    <h3>Photo Organization Tools</h3>
                    <div class="bulk-controls">
                        <button class="btn btn-primary" onclick="loadUnlinkedPhotos()">Load Unlinked Photos</button>
                        <button class="btn btn-secondary" onclick="loadAllPhotos()">Load All Photos</button>
                        <button class="btn btn-warning" onclick="bulkSelectPhotos()">Bulk Select Mode</button>
                        <span class="selected-count" id="selected-count" style="display: none;">0 selected</span>
                    </div>
                    
                    <div id="bulk-photo-actions" style="display: none; margin-top: 15px;">
                        <h4>Apply to Selected Photos:</h4>
                        <div class="metadata-form">
                            <div class="form-group">
                                <label>Cat Name</label>
                                <input type="text" id="bulk-cat-name" placeholder="Cat name">
                            </div>
                            <div class="form-group">
                                <label>Breed</label>
                                <input type="text" id="bulk-breed" value="Maine Coon">
                            </div>
                            <div class="form-group">
                                <label>Bloodline</label>
                                <input type="text" id="bulk-bloodline" placeholder="Bloodline">
                            </div>
                            <div class="form-group">
                                <label>Age/Stage</label>
                                <select id="bulk-age">
                                    <option value="">Select age/stage</option>
                                    <option value="newborn">Newborn (0-2 weeks)</option>
                                    <option value="kitten-young">Young Kitten (2-8 weeks)</option>
                                    <option value="kitten">Kitten (8 weeks - 6 months)</option>
                                    <option value="young-adult">Young Adult (6 months - 2 years)</option>
                                    <option value="adult">Adult (2+ years)</option>
                                </select>
                            </div>
                        </div>
                        <div class="bulk-controls" style="margin-top: 15px;">
                            <button class="btn btn-success" onclick="applyBulkMetadata()">Apply Metadata</button>
                            <button class="btn btn-secondary" onclick="clearSelection()">Clear Selection</button>
                        </div>
                    </div>
                </div>

                <div class="cat-grid" id="photos-grid">
                    <p>Click "Load Unlinked Photos" to start organizing photos</p>
                </div>
            </div>

            <!-- Bulk Operations Tab -->
            <div class="tab-panel" id="bulk-panel">
                <h2>Advanced Bulk Operations</h2>
                
                <div class="bulk-actions">
                    <h3>Litter Management</h3>
                    <p>Process entire litters efficiently with common metadata</p>
                    <div class="metadata-form">
                        <div class="form-group">
                            <label>Litter Date (Birth Date)</label>
                            <input type="date" id="litter-birth-date">
                        </div>
                        <div class="form-group">
                            <label>Mother Cat ID</label>
                            <input type="text" id="litter-mother-id" placeholder="Mother's cat ID">
                        </div>
                        <div class="form-group">
                            <label>Father Cat ID</label>
                            <input type="text" id="litter-father-id" placeholder="Father's cat ID">
                        </div>
                        <div class="form-group">
                            <label>Bloodline</label>
                            <input type="text" id="litter-bloodline" placeholder="Primary bloodline">
                        </div>
                        <div class="form-group">
                            <label>Number of Kittens</label>
                            <input type="number" id="litter-count" min="1" max="10">
                        </div>
                    </div>
                    <div class="bulk-controls">
                        <button class="btn btn-success" onclick="processLitterWizard()">Start Litter Wizard</button>
                        <button class="btn btn-primary" onclick="generateKittenProfiles()">Generate Kitten Profiles</button>
                    </div>
                </div>

                <div class="bulk-actions" style="margin-top: 30px;">
                    <h3>Bloodline Operations</h3>
                    <p>Apply bloodline information across multiple cats</p>
                    <div class="metadata-form">
                        <div class="form-group">
                            <label>Select Bloodline</label>
                            <select id="bloodline-select">
                                <option value="">Choose bloodline...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Champion Title</label>
                            <input type="text" id="champion-title" placeholder="e.g., International Champion">
                        </div>
                        <div class="form-group">
                            <label>Generation Level</label>
                            <select id="generation-level">
                                <option value="1">Parent Generation</option>
                                <option value="2">Grandparent Generation</option>
                                <option value="3">Great-Grandparent Generation</option>
                            </select>
                        </div>
                    </div>
                    <div class="bulk-controls">
                        <button class="btn btn-warning" onclick="propagateBloodline()">Propagate Bloodline</button>
                        <button class="btn btn-secondary" onclick="validatePedigree()">Validate Pedigree</button>
                    </div>
                </div>

                <div id="bulk-progress" style="display: none;">
                    <h3>Processing Progress</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div id="progress-text">Processing...</div>
                </div>
            </div>

            <!-- Pedigree Builder Tab -->
            <div class="tab-panel" id="pedigree-panel">
                <h2>Pedigree & Family Tree Builder</h2>
                
                <div class="bulk-actions">
                    <h3>Family Relationship Management</h3>
                    <div class="metadata-form">
                        <div class="form-group">
                            <label>Select Cat</label>
                            <select id="pedigree-cat-select">
                                <option value="">Choose cat...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Father</label>
                            <select id="father-select">
                                <option value="">Choose father...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Mother</label>
                            <select id="mother-select">
                                <option value="">Choose mother...</option>
                            </select>
                        </div>
                    </div>
                    <div class="bulk-controls">
                        <button class="btn btn-success" onclick="updateFamilyRelationships()">Update Relationships</button>
                        <button class="btn btn-primary" onclick="generateFamilyTree()">Generate Family Tree</button>
                        <button class="btn btn-warning" onclick="validateRelationships()">Validate Relationships</button>
                    </div>
                </div>

                <div class="family-tree" id="family-tree-display">
                    <h3>Family Tree Visualization</h3>
                    <p>Select a cat above to view its family tree</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Modals -->
    <div id="cat-edit-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Edit Cat Profile</h2>
            <div id="cat-edit-form">
                <!-- Cat editing form will be populated here -->
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let selectedPhotos = new Set();
        let allCats = [];
        let allPhotos = [];
        let isSelectMode = false;
        let autoSaveTimer = null;
        let isDirty = false;
        const apiBaseUrl = 'http://localhost:5002/api';
        const token = localStorage.getItem('admin_token');

        // Tab management
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.dataset.tab;
                switchToTab(tabName);
            });
        });

        function switchToTab(tabName) {
            // Update buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // Update panels
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            document.getElementById(`${tabName}-panel`).classList.add('active');

            // Load tab-specific data
            loadTabData(tabName);
        }

        function loadTabData(tabName) {
            switch(tabName) {
                case 'overview':
                    loadOverviewData();
                    break;
                case 'cats':
                    loadCatProfiles();
                    break;
                case 'photos':
                    // Photos loaded on demand
                    break;
                case 'bulk':
                    loadBloodlineOptions();
                    break;
                case 'pedigree':
                    loadPedigreeCats();
                    break;
            }
        }

        // API Helper Functions
        async function apiRequest(endpoint, options = {}) {
            const defaultOptions = {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            };

            const response = await fetch(`${apiBaseUrl}${endpoint}`, {
                ...defaultOptions,
                ...options,
                headers: { ...defaultOptions.headers, ...options.headers }
            });

            if (!response.ok) {
                throw new Error(`API Error: ${response.status} ${response.statusText}`);
            }

            return response.json();
        }

        function showLoading() {
            document.getElementById('loading-overlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loading-overlay').style.display = 'none';
        }

        function showAlert(message, type = 'success') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            // Insert at top of current tab
            const activePanel = document.querySelector('.tab-panel.active');
            activePanel.insertBefore(alert, activePanel.firstChild);
            
            setTimeout(() => alert.remove(), 5000);
        }

        // Overview Tab Functions
        async function loadOverviewData() {
            try {
                showLoading();
                
                const [catsData, unlinkedData] = await Promise.all([
                    apiRequest('/Admin/cats/list-all'),
                    apiRequest('/Admin/photos/unlinked')
                ]);

                document.getElementById('total-cats').textContent = catsData.totalCats || 0;
                document.getElementById('total-photos').textContent = catsData.totalPhotos || 0;
                document.getElementById('unlinked-photos').textContent = unlinkedData.count || 0;
                
                // Count available kittens
                const availableKittens = catsData.cats?.filter(cat => 
                    cat.availabilityStatus === 'available' || 
                    cat.breedingStatus === 'available-kitten'
                ).length || 0;
                document.getElementById('available-kittens').textContent = availableKittens;

            } catch (error) {
                console.error('Error loading overview:', error);
                showAlert('Error loading overview data', 'error');
            } finally {
                hideLoading();
            }
        }

        // Cat Profiles Functions
        async function loadCatProfiles() {
            try {
                showLoading();
                const data = await apiRequest('/Admin/cats/list-all');
                allCats = data.cats || [];
                renderCatProfiles(allCats);
                populateBloodlineFilter();
            } catch (error) {
                console.error('Error loading cats:', error);
                showAlert('Error loading cat profiles', 'error');
            } finally {
                hideLoading();
            }
        }

        function renderCatProfiles(cats) {
            const grid = document.getElementById('cat-profiles-grid');
            
            if (!cats || cats.length === 0) {
                grid.innerHTML = '<p>No cat profiles found. Create your first cat profile to get started.</p>';
                return;
            }

            grid.innerHTML = cats.map(cat => `
                <div class="cat-card">
                    <div class="cat-header">
                        <div class="cat-name">${cat.catName || 'Unknown Cat'}</div>
                        <div class="cat-info">
                            ${cat.breed} • ${cat.bloodline || 'No bloodline'}<br>
                            ${cat.photoCount} photo${cat.photoCount !== 1 ? 's' : ''} • 
                            ${cat.breedingStatus || 'Unknown status'}
                        </div>
                    </div>
                    <div class="cat-photos">
                        ${cat.photos.slice(0, 6).map(photo => `
                            <img src="${photo.url}" alt="Photo" class="photo-thumbnail" 
                                 onclick="viewPhoto('${photo.s3Key}')">
                        `).join('')}
                        ${cat.photos.length > 6 ? `<span>+${cat.photos.length - 6} more</span>` : ''}
                    </div>
                    <div style="padding: 15px; border-top: 1px solid #eee;">
                        <button class="btn btn-primary" onclick="editCat('${cat.catId}', '${cat.catName}')">
                            Edit Profile
                        </button>
                        <button class="btn btn-secondary" onclick="viewPedigree('${cat.catId}')">
                            View Pedigree
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function populateBloodlineFilter() {
            const bloodlines = [...new Set(allCats.map(cat => cat.bloodline).filter(Boolean))];
            const select = document.getElementById('bloodline-filter');
            
            select.innerHTML = '<option value="">All Bloodlines</option>' +
                bloodlines.map(bloodline => `<option value="${bloodline}">${bloodline}</option>`).join('');
        }

        // Photo Management Functions
        async function loadUnlinkedPhotos() {
            try {
                showLoading();
                const data = await apiRequest('/Admin/photos/unlinked');
                allPhotos = data.photos || [];
                renderPhotos(allPhotos, 'Unlinked Photos');
            } catch (error) {
                console.error('Error loading unlinked photos:', error);
                showAlert('Error loading unlinked photos', 'error');
            } finally {
                hideLoading();
            }
        }

        async function loadAllPhotos() {
            try {
                showLoading();
                const data = await apiRequest('/Admin/cats/list-all');
                allPhotos = [];
                data.cats?.forEach(cat => {
                    cat.photos.forEach(photo => {
                        allPhotos.push({...photo, catName: cat.catName});
                    });
                });
                renderPhotos(allPhotos, 'All Photos');
            } catch (error) {
                console.error('Error loading all photos:', error);
                showAlert('Error loading all photos', 'error');
            } finally {
                hideLoading();
            }
        }

        function renderPhotos(photos, title) {
            const grid = document.getElementById('photos-grid');
            
            if (!photos || photos.length === 0) {
                grid.innerHTML = `<p>No ${title.toLowerCase()} found.</p>`;
                return;
            }

            grid.innerHTML = `
                <h3>${title} (${photos.length})</h3>
                <div class="cat-grid">
                    ${photos.map(photo => `
                        <div class="cat-card">
                            <div class="cat-header">
                                <div class="cat-name">${photo.catName || 'Unlinked Photo'}</div>
                                <div class="cat-info">
                                    Size: ${(photo.size / 1024).toFixed(1)} KB<br>
                                    Modified: ${new Date(photo.lastModified).toLocaleDateString()}
                                </div>
                            </div>
                            <div class="cat-photos">
                                <img src="${photo.url}" alt="Photo" class="photo-thumbnail ${selectedPhotos.has(photo.s3Key) ? 'selected' : ''}" 
                                     onclick="togglePhotoSelection('${photo.s3Key}')" style="width: 100%; height: 200px;">
                            </div>
                            <div style="padding: 15px; border-top: 1px solid #eee;">
                                <button class="btn btn-primary" onclick="editPhotoMetadata('${photo.s3Key}')">
                                    Edit Metadata
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function togglePhotoSelection(s3Key) {
            if (!isSelectMode) return;
            
            if (selectedPhotos.has(s3Key)) {
                selectedPhotos.delete(s3Key);
            } else {
                selectedPhotos.add(s3Key);
            }
            
            updateSelectedCount();
            
            // Update visual selection
            const img = document.querySelector(`img[onclick*="${s3Key}"]`);
            if (img) {
                img.classList.toggle('selected', selectedPhotos.has(s3Key));
            }
        }

        function bulkSelectPhotos() {
            isSelectMode = !isSelectMode;
            const button = event.target;
            const actionsDiv = document.getElementById('bulk-photo-actions');
            
            if (isSelectMode) {
                button.textContent = 'Exit Select Mode';
                button.className = 'btn btn-danger';
                actionsDiv.style.display = 'block';
                showAlert('Click photos to select them for bulk operations', 'warning');
            } else {
                button.textContent = 'Bulk Select Mode';
                button.className = 'btn btn-warning';
                actionsDiv.style.display = 'none';
                clearSelection();
            }
        }

        function clearSelection() {
            selectedPhotos.clear();
            updateSelectedCount();
            document.querySelectorAll('.photo-thumbnail.selected').forEach(img => {
                img.classList.remove('selected');
            });
        }

        function updateSelectedCount() {
            const countElement = document.getElementById('selected-count');
            if (selectedPhotos.size > 0) {
                countElement.textContent = `${selectedPhotos.size} selected`;
                countElement.style.display = 'inline';
            } else {
                countElement.style.display = 'none';
            }
        }

        async function applyBulkMetadata() {
            if (selectedPhotos.size === 0) {
                showAlert('No photos selected', 'warning');
                return;
            }

            const bulkData = {
                catName: document.getElementById('bulk-cat-name').value,
                breed: document.getElementById('bulk-breed').value,
                bloodline: document.getElementById('bulk-bloodline').value,
                age: document.getElementById('bulk-age').value
            };

            // Filter out empty values
            const metadata = Object.fromEntries(
                Object.entries(bulkData).filter(([key, value]) => value.trim() !== '')
            );

            if (Object.keys(metadata).length === 0) {
                showAlert('Please enter at least one metadata field', 'warning');
                return;
            }

            try {
                showLoading();
                const requests = Array.from(selectedPhotos).map(s3Key => ({
                    s3Key,
                    ...metadata
                }));

                const response = await apiRequest('/S3Metadata/bulk-update', {
                    method: 'POST',
                    body: JSON.stringify(requests)
                });

                showAlert(`Successfully updated ${selectedPhotos.size} photos`, 'success');
                clearSelection();
                
                // Refresh the current photo view
                if (allPhotos.length > 0) {
                    loadUnlinkedPhotos();
                }
            } catch (error) {
                console.error('Error applying bulk metadata:', error);
                showAlert('Error applying bulk metadata', 'error');
            } finally {
                hideLoading();
            }
        }

        // Placeholder functions for features to be implemented
        function refreshData() {
            loadTabData(document.querySelector('.tab-button.active').dataset.tab);
            showAlert('Data refreshed', 'success');
        }

        async function exportMetadata() {
            try {
                showLoading();

                // Get all cat data
                const catsData = await apiRequest('/Admin/cats/list-all');

                if (!catsData.success || !catsData.cats) {
                    showAlert('Failed to load cat data for export', 'error');
                    return;
                }

                // Convert to CSV format
                const csvHeaders = [
                    'Cat Name', 'Cat ID', 'Breed', 'Bloodline', 'Gender', 'Breeding Status',
                    'Registered Name', 'Registration Number', 'Father ID', 'Mother ID',
                    'Birth Date', 'Champion Titles', 'Photo Count', 'Notes'
                ];

                const csvRows = catsData.cats.map(cat => [
                    cat.catName || '',
                    cat.catId || '',
                    cat.breed || '',
                    cat.bloodline || '',
                    cat.gender || '',
                    cat.breedingStatus || '',
                    cat.registeredName || '',
                    cat.registrationNumber || '',
                    cat.fatherId || '',
                    cat.motherId || '',
                    cat.birthDate ? cat.birthDate.split('T')[0] : '',
                    cat.championTitles || '',
                    cat.photoCount || 0,
                    cat.notes || ''
                ]);

                // Create CSV content
                const csvContent = [
                    csvHeaders.join(','),
                    ...csvRows.map(row => row.map(field => `"${field}"`).join(','))
                ].join('\n');

                // Download CSV file
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `yendor-cats-metadata-${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showAlert('Metadata exported successfully', 'success');

            } catch (error) {
                console.error('Error exporting metadata:', error);
                showAlert('Error exporting metadata', 'error');
            } finally {
                hideLoading();
            }
        }

        async function showS3Config() {
            try {
                const data = await apiRequest('/Admin/s3/config');
                alert(`S3 Configuration:\nBucket: ${data.s3Config.bucketName}\nRegion: ${data.s3Config.region}\nCDN: ${data.s3Config.useCdn ? 'Enabled' : 'Disabled'}`);
            } catch (error) {
                showAlert('Error loading S3 configuration', 'error');
            }
        }

        function searchCats() {
            const searchTerm = document.getElementById('cat-search').value.toLowerCase();
            const breedFilter = document.getElementById('breed-filter').value;
            const bloodlineFilter = document.getElementById('bloodline-filter').value;
            const statusFilter = document.getElementById('status-filter').value;

            let filteredCats = allCats;

            // Apply search term filter
            if (searchTerm) {
                filteredCats = filteredCats.filter(cat =>
                    cat.catName.toLowerCase().includes(searchTerm) ||
                    (cat.registeredName && cat.registeredName.toLowerCase().includes(searchTerm)) ||
                    (cat.catId && cat.catId.toLowerCase().includes(searchTerm))
                );
            }

            // Apply breed filter
            if (breedFilter) {
                filteredCats = filteredCats.filter(cat => cat.breed === breedFilter);
            }

            // Apply bloodline filter
            if (bloodlineFilter) {
                filteredCats = filteredCats.filter(cat => cat.bloodline === bloodlineFilter);
            }

            // Apply status filter
            if (statusFilter) {
                filteredCats = filteredCats.filter(cat => cat.breedingStatus === statusFilter);
            }

            // Render filtered results
            renderCatProfiles(filteredCats);

            // Show search results count
            const resultCount = filteredCats.length;
            const totalCount = allCats.length;
            showAlert(`Found ${resultCount} of ${totalCount} cats`, 'info');
        }

        function clearFilters() {
            document.getElementById('cat-search').value = '';
            document.getElementById('breed-filter').value = '';
            document.getElementById('bloodline-filter').value = '';
            document.getElementById('status-filter').value = '';
            loadCatProfiles();
        }

        function createNewCatProfile() {
            // Create an empty profile for new cat
            const emptyProfile = {
                catId: '',
                catName: '',
                breed: 'Maine Coon',
                bloodline: '',
                registeredName: '',
                registrationNumber: '',
                gender: '',
                breedingStatus: '',
                fatherId: '',
                motherId: '',
                birthDate: '',
                championTitles: '',
                notes: ''
            };

            showCatEditModal(emptyProfile);
        }

        async function editCat(catId, catName) {
            try {
                showLoading();

                // Fetch the cat profile data
                const profileData = await apiRequest(`/CatManagement/profiles/${catId}`);

                if (!profileData.success) {
                    showAlert('Failed to load cat profile', 'error');
                    return;
                }

                // Show the edit modal
                showCatEditModal(profileData.profile);

            } catch (error) {
                console.error('Error loading cat profile:', error);
                showAlert('Error loading cat profile', 'error');
            } finally {
                hideLoading();
            }
        }

        function showCatEditModal(profile) {
            const modal = document.getElementById('cat-edit-modal');
            const form = document.getElementById('cat-edit-form');

            // Create the edit form
            form.innerHTML = `
                <form id="cat-profile-form" class="metadata-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-cat-name">Cat Name *</label>
                            <input type="text" id="edit-cat-name" value="${profile.catName || ''}" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-cat-id">Cat ID *</label>
                            <input type="text" id="edit-cat-id" value="${profile.catId || ''}" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-breed">Breed</label>
                            <input type="text" id="edit-breed" value="${profile.breed || 'Maine Coon'}">
                        </div>
                        <div class="form-group">
                            <label for="edit-bloodline">Bloodline</label>
                            <input type="text" id="edit-bloodline" value="${profile.bloodline || ''}">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-registered-name">Registered Name</label>
                            <input type="text" id="edit-registered-name" value="${profile.registeredName || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-registration-number">Registration Number</label>
                            <input type="text" id="edit-registration-number" value="${profile.registrationNumber || ''}">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-gender">Gender</label>
                            <select id="edit-gender">
                                <option value="">Select gender</option>
                                <option value="Male" ${profile.gender === 'Male' ? 'selected' : ''}>Male</option>
                                <option value="Female" ${profile.gender === 'Female' ? 'selected' : ''}>Female</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-breeding-status">Breeding Status</label>
                            <select id="edit-breeding-status">
                                <option value="">Select status</option>
                                <option value="Active Breeder" ${profile.breedingStatus === 'Active Breeder' ? 'selected' : ''}>Active Breeder</option>
                                <option value="Retired" ${profile.breedingStatus === 'Retired' ? 'selected' : ''}>Retired</option>
                                <option value="Kitten" ${profile.breedingStatus === 'Kitten' ? 'selected' : ''}>Kitten</option>
                                <option value="Not for Breeding" ${profile.breedingStatus === 'Not for Breeding' ? 'selected' : ''}>Not for Breeding</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-father-id">Father ID</label>
                            <input type="text" id="edit-father-id" value="${profile.fatherId || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-mother-id">Mother ID</label>
                            <input type="text" id="edit-mother-id" value="${profile.motherId || ''}">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-birth-date">Birth Date</label>
                            <input type="date" id="edit-birth-date" value="${profile.birthDate ? profile.birthDate.split('T')[0] : ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-champion-titles">Champion Titles</label>
                            <input type="text" id="edit-champion-titles" value="${profile.championTitles || ''}" placeholder="e.g., International Champion">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit-notes">Notes</label>
                        <textarea id="edit-notes" rows="3">${profile.notes || ''}</textarea>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeCatEditModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            `;

            // Add form submit handler
            document.getElementById('cat-profile-form').addEventListener('submit', saveCatProfile);

            // Add auto-save functionality
            setupAutoSave();

            // Add form validation
            setupFormValidation();

            // Show modal
            modal.style.display = 'block';
        }

        function viewPedigree(catId) {
            showAlert('Pedigree view coming soon', 'warning');
        }

        function setupAutoSave() {
            const form = document.getElementById('cat-profile-form');
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    isDirty = true;
                    clearTimeout(autoSaveTimer);
                    autoSaveTimer = setTimeout(() => {
                        if (isDirty) {
                            autoSaveDraft();
                        }
                    }, 2000); // Auto-save after 2 seconds of inactivity
                });
            });
        }

        function setupFormValidation() {
            const form = document.getElementById('cat-profile-form');
            const inputs = form.querySelectorAll('input[required], select[required]');

            inputs.forEach(input => {
                input.addEventListener('blur', validateField);
                input.addEventListener('input', clearFieldError);
            });
        }

        function validateField(event) {
            const field = event.target;
            const value = field.value.trim();

            // Remove existing error styling
            field.classList.remove('error');

            // Check if required field is empty
            if (field.hasAttribute('required') && !value) {
                showFieldError(field, 'This field is required');
                return false;
            }

            // Validate Cat ID format (alphanumeric with hyphens)
            if (field.id === 'edit-cat-id' && value) {
                if (!/^[a-zA-Z0-9-]+$/.test(value)) {
                    showFieldError(field, 'Cat ID can only contain letters, numbers, and hyphens');
                    return false;
                }
            }

            // Validate email format if it's an email field
            if (field.type === 'email' && value) {
                if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    showFieldError(field, 'Please enter a valid email address');
                    return false;
                }
            }

            return true;
        }

        function showFieldError(field, message) {
            field.classList.add('error');

            // Remove existing error message
            const existingError = field.parentNode.querySelector('.field-error');
            if (existingError) {
                existingError.remove();
            }

            // Add new error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        }

        function clearFieldError(event) {
            const field = event.target;
            field.classList.remove('error');
            const errorDiv = field.parentNode.querySelector('.field-error');
            if (errorDiv) {
                errorDiv.remove();
            }
        }

        function autoSaveDraft() {
            try {
                const formData = collectFormData();
                localStorage.setItem('cat-profile-draft', JSON.stringify({
                    data: formData,
                    timestamp: new Date().toISOString()
                }));

                // Show subtle indication of auto-save
                showAutoSaveIndicator();
                isDirty = false;
            } catch (error) {
                console.error('Auto-save failed:', error);
            }
        }

        function showAutoSaveIndicator() {
            const indicator = document.getElementById('auto-save-indicator') || createAutoSaveIndicator();
            indicator.textContent = 'Draft saved';
            indicator.style.opacity = '1';

            setTimeout(() => {
                indicator.style.opacity = '0';
            }, 2000);
        }

        function createAutoSaveIndicator() {
            const indicator = document.createElement('div');
            indicator.id = 'auto-save-indicator';
            indicator.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 1002;
            `;
            document.body.appendChild(indicator);
            return indicator;
        }

        function collectFormData() {
            return {
                catId: document.getElementById('edit-cat-id')?.value || '',
                catName: document.getElementById('edit-cat-name')?.value || '',
                breed: document.getElementById('edit-breed')?.value || '',
                bloodline: document.getElementById('edit-bloodline')?.value || '',
                registeredName: document.getElementById('edit-registered-name')?.value || '',
                registrationNumber: document.getElementById('edit-registration-number')?.value || '',
                gender: document.getElementById('edit-gender')?.value || '',
                breedingStatus: document.getElementById('edit-breeding-status')?.value || '',
                fatherId: document.getElementById('edit-father-id')?.value || '',
                motherId: document.getElementById('edit-mother-id')?.value || '',
                birthDate: document.getElementById('edit-birth-date')?.value || '',
                championTitles: document.getElementById('edit-champion-titles')?.value || '',
                notes: document.getElementById('edit-notes')?.value || ''
            };
        }

        function closeCatEditModal() {
            // Check for unsaved changes
            if (isDirty) {
                if (!confirm('You have unsaved changes. Are you sure you want to close?')) {
                    return;
                }
            }

            // Clear auto-save timer
            clearTimeout(autoSaveTimer);

            // Clear draft
            localStorage.removeItem('cat-profile-draft');

            const modal = document.getElementById('cat-edit-modal');
            modal.style.display = 'none';
            isDirty = false;
        }

        async function saveCatProfile(event) {
            event.preventDefault();

            try {
                showLoading();

                // Collect form data
                const profileData = {
                    catId: document.getElementById('edit-cat-id').value,
                    catName: document.getElementById('edit-cat-name').value,
                    breed: document.getElementById('edit-breed').value,
                    bloodline: document.getElementById('edit-bloodline').value,
                    registeredName: document.getElementById('edit-registered-name').value,
                    registrationNumber: document.getElementById('edit-registration-number').value,
                    gender: document.getElementById('edit-gender').value,
                    breedingStatus: document.getElementById('edit-breeding-status').value,
                    fatherId: document.getElementById('edit-father-id').value,
                    motherId: document.getElementById('edit-mother-id').value,
                    birthDate: document.getElementById('edit-birth-date').value,
                    championTitles: document.getElementById('edit-champion-titles').value,
                    notes: document.getElementById('edit-notes').value,
                    updatedAt: new Date().toISOString()
                };

                // Validate required fields
                if (!profileData.catId || !profileData.catName) {
                    showAlert('Cat ID and Cat Name are required', 'error');
                    return;
                }

                // Save the profile
                const result = await apiRequest('/CatManagement/profiles', {
                    method: 'POST',
                    body: JSON.stringify(profileData)
                });

                if (result.success) {
                    showAlert(`Cat profile saved successfully: ${profileData.catName}`, 'success');
                    closeCatEditModal();
                    loadCatProfiles(); // Refresh the cat list
                } else {
                    showAlert(result.message || 'Failed to save cat profile', 'error');
                }

            } catch (error) {
                console.error('Error saving cat profile:', error);
                showAlert('Error saving cat profile', 'error');
            } finally {
                hideLoading();
            }
        }

        function viewPhoto(s3Key) {
            window.open(`${apiBaseUrl.replace('/api', '')}/photos/${s3Key}`, '_blank');
        }

        async function editPhotoMetadata(s3Key) {
            try {
                showLoading();

                // Fetch photo metadata
                const photoData = await apiRequest(`/S3Metadata/get/${encodeURIComponent(s3Key)}`);

                if (!photoData || !photoData.metadata) {
                    showAlert('Failed to load photo metadata', 'error');
                    return;
                }

                showPhotoEditModal(s3Key, photoData.metadata);

            } catch (error) {
                console.error('Error loading photo metadata:', error);
                showAlert('Error loading photo metadata', 'error');
            } finally {
                hideLoading();
            }
        }

        function showPhotoEditModal(s3Key, metadata) {
            const modal = document.getElementById('photo-edit-modal') || createPhotoEditModal();
            const form = document.getElementById('photo-edit-form');

            // Create the edit form with comprehensive metadata fields
            form.innerHTML = `
                <form id="photo-metadata-form" class="metadata-form">
                    <input type="hidden" id="photo-s3-key" value="${s3Key}">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="photo-cat-name">Cat Name *</label>
                            <input type="text" id="photo-cat-name" value="${metadata['cat-name'] || metadata.name || ''}" required>
                        </div>
                        <div class="form-group">
                            <label for="photo-breed">Breed</label>
                            <input type="text" id="photo-breed" value="${metadata.breed || 'Maine Coon'}">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="photo-bloodline">Bloodline</label>
                            <input type="text" id="photo-bloodline" value="${metadata.bloodline || ''}">
                        </div>
                        <div class="form-group">
                            <label for="photo-age">Age at Photo</label>
                            <input type="text" id="photo-age" value="${metadata['age-at-photo'] || metadata.ageAtPhoto || ''}" placeholder="e.g., 6 months">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="photo-gender">Gender</label>
                            <select id="photo-gender">
                                <option value="">Select gender</option>
                                <option value="M" ${metadata.gender === 'M' ? 'selected' : ''}>Male</option>
                                <option value="F" ${metadata.gender === 'F' ? 'selected' : ''}>Female</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="photo-type">Photo Type</label>
                            <select id="photo-type">
                                <option value="">Select type</option>
                                <option value="profile" ${metadata['photo-type'] === 'profile' ? 'selected' : ''}>Profile</option>
                                <option value="action" ${metadata['photo-type'] === 'action' ? 'selected' : ''}>Action</option>
                                <option value="family" ${metadata['photo-type'] === 'family' ? 'selected' : ''}>Family</option>
                                <option value="breeding" ${metadata['photo-type'] === 'breeding' ? 'selected' : ''}>Breeding</option>
                                <option value="growth" ${metadata['photo-type'] === 'growth' ? 'selected' : ''}>Growth</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="photo-cat-id">Cat ID</label>
                            <input type="text" id="photo-cat-id" value="${metadata['cat-id'] || ''}" placeholder="Database Cat ID">
                        </div>
                        <div class="form-group">
                            <label for="photo-registered-name">Registered Name</label>
                            <input type="text" id="photo-registered-name" value="${metadata['registered-name'] || ''}" placeholder="Official pedigree name">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="photo-mother-id">Mother Cat ID</label>
                            <input type="text" id="photo-mother-id" value="${metadata['mother-id'] || ''}" placeholder="Mother's Cat ID">
                        </div>
                        <div class="form-group">
                            <label for="photo-father-id">Father Cat ID</label>
                            <input type="text" id="photo-father-id" value="${metadata['father-id'] || ''}" placeholder="Father's Cat ID">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="photo-breeding-status">Breeding Status</label>
                            <select id="photo-breeding-status">
                                <option value="">Select status</option>
                                <option value="available-kitten" ${metadata['breeding-status'] === 'available-kitten' ? 'selected' : ''}>Available Kitten</option>
                                <option value="breeding-queen" ${metadata['breeding-status'] === 'breeding-queen' ? 'selected' : ''}>Breeding Queen</option>
                                <option value="stud" ${metadata['breeding-status'] === 'stud' ? 'selected' : ''}>Stud</option>
                                <option value="retired" ${metadata['breeding-status'] === 'retired' ? 'selected' : ''}>Retired</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="photo-availability-status">Availability Status</label>
                            <select id="photo-availability-status">
                                <option value="">Select status</option>
                                <option value="available" ${metadata['availability-status'] === 'available' ? 'selected' : ''}>Available</option>
                                <option value="reserved" ${metadata['availability-status'] === 'reserved' ? 'selected' : ''}>Reserved</option>
                                <option value="sold" ${metadata['availability-status'] === 'sold' ? 'selected' : ''}>Sold</option>
                                <option value="not-for-sale" ${metadata['availability-status'] === 'not-for-sale' ? 'selected' : ''}>Not for Sale</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="photo-tags">Tags</label>
                        <input type="text" id="photo-tags" value="${metadata.tags || ''}" placeholder="Comma-separated tags">
                    </div>

                    <div class="form-group">
                        <label for="photo-description">Description</label>
                        <textarea id="photo-description" rows="3">${metadata.description || ''}</textarea>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closePhotoEditModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            `;

            // Add form submit handler
            document.getElementById('photo-metadata-form').addEventListener('submit', savePhotoMetadata);

            // Show modal
            modal.style.display = 'block';
        }

        function createPhotoEditModal() {
            const modal = document.createElement('div');
            modal.id = 'photo-edit-modal';
            modal.className = 'modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h2>Edit Photo Metadata</h2>
                    <div id="photo-edit-form">
                        <!-- Photo editing form will be populated here -->
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Add close handler
            modal.querySelector('.close').addEventListener('click', closePhotoEditModal);

            return modal;
        }

        function closePhotoEditModal() {
            const modal = document.getElementById('photo-edit-modal');
            if (modal) {
                modal.style.display = 'none';
            }
        }

        async function savePhotoMetadata(event) {
            event.preventDefault();

            try {
                showLoading();

                const s3Key = document.getElementById('photo-s3-key').value;

                // Collect form data in the format expected by S3MetadataController
                const updateRequest = {
                    s3Key: s3Key,
                    catName: document.getElementById('photo-cat-name').value,
                    breed: document.getElementById('photo-breed').value,
                    bloodline: document.getElementById('photo-bloodline').value,
                    ageAtPhoto: document.getElementById('photo-age').value,
                    gender: document.getElementById('photo-gender').value,
                    photoType: document.getElementById('photo-type').value,
                    tags: document.getElementById('photo-tags').value,
                    description: document.getElementById('photo-description').value,
                    catId: document.getElementById('photo-cat-id').value,
                    registeredName: document.getElementById('photo-registered-name').value,
                    motherId: document.getElementById('photo-mother-id').value,
                    fatherId: document.getElementById('photo-father-id').value,
                    breedingStatus: document.getElementById('photo-breeding-status').value,
                    availabilityStatus: document.getElementById('photo-availability-status').value
                };

                // Filter out empty values
                const cleanedRequest = {};
                for (const [key, value] of Object.entries(updateRequest)) {
                    if (value && value.trim() !== '') {
                        cleanedRequest[key] = value.trim();
                    }
                }

                // Ensure s3Key is always present
                cleanedRequest.s3Key = s3Key;

                // Save the metadata
                const result = await apiRequest('/S3Metadata/update', {
                    method: 'POST',
                    body: JSON.stringify(cleanedRequest)
                });

                if (result.success) {
                    showAlert('Photo metadata saved successfully', 'success');
                    closePhotoEditModal();
                    // Refresh the current tab data
                    const activeTab = document.querySelector('.tab-button.active').dataset.tab;
                    loadTabData(activeTab);
                } else {
                    showAlert(result.message || 'Failed to save photo metadata', 'error');
                }

            } catch (error) {
                console.error('Error saving photo metadata:', error);
                showAlert('Error saving photo metadata', 'error');
            } finally {
                hideLoading();
            }
        }

        function loadBloodlineOptions() {
            // Populate bloodline options from existing cats
            if (allCats.length === 0) {
                loadCatProfiles().then(() => {
                    populateBloodlineSelect();
                });
            } else {
                populateBloodlineSelect();
            }
        }

        function populateBloodlineSelect() {
            const bloodlines = [...new Set(allCats.map(cat => cat.bloodline).filter(Boolean))];
            const select = document.getElementById('bloodline-select');
            
            select.innerHTML = '<option value="">Choose bloodline...</option>' +
                bloodlines.map(bloodline => `<option value="${bloodline}">${bloodline}</option>`).join('');
        }

        function loadPedigreeCats() {
            // Populate cat selects for pedigree management
            if (allCats.length === 0) {
                loadCatProfiles().then(() => {
                    populateCatSelects();
                });
            } else {
                populateCatSelects();
            }
        }

        function populateCatSelects() {
            const catOptions = allCats.map(cat => 
                `<option value="${cat.catId}">${cat.catName} (${cat.breed})</option>`
            ).join('');

            document.getElementById('pedigree-cat-select').innerHTML = '<option value="">Choose cat...</option>' + catOptions;
            document.getElementById('father-select').innerHTML = '<option value="">Choose father...</option>' + catOptions;
            document.getElementById('mother-select').innerHTML = '<option value="">Choose mother...</option>' + catOptions;
        }

        // Bulk operation functions
        async function processLitterWizard() {
            try {
                showLoading();

                // Collect litter data from form
                const litterData = {
                    litterPrefix: document.getElementById('litter-prefix').value,
                    birthDate: document.getElementById('litter-birth-date').value,
                    motherId: document.getElementById('litter-mother-id').value,
                    fatherId: document.getElementById('litter-father-id').value,
                    bloodline: document.getElementById('litter-bloodline').value,
                    kittenCount: parseInt(document.getElementById('litter-count').value),
                    photoKeys: Array.from(selectedPhotos) // Use selected photos
                };

                // Validate required fields
                if (!litterData.litterPrefix || !litterData.birthDate || !litterData.kittenCount) {
                    showAlert('Litter prefix, birth date, and kitten count are required', 'error');
                    return;
                }

                if (litterData.photoKeys.length === 0) {
                    showAlert('Please select photos to process as a litter', 'error');
                    return;
                }

                // Process the litter
                const result = await apiRequest('/CatManagement/litter/process', {
                    method: 'POST',
                    body: JSON.stringify(litterData)
                });

                if (result.success) {
                    showAlert(`Litter processed successfully: ${result.successfulItems} photos updated`, 'success');
                    selectedPhotos.clear();
                    loadTabData('bulk'); // Refresh the bulk operations tab
                } else {
                    showAlert(result.message || 'Failed to process litter', 'error');
                }

            } catch (error) {
                console.error('Error processing litter:', error);
                showAlert('Error processing litter', 'error');
            } finally {
                hideLoading();
            }
        }

        function generateKittenProfiles() {
            showAlert('Kitten profile generation coming soon', 'warning');
        }

        async function propagateBloodline() {
            try {
                const bloodline = document.getElementById('bloodline-select').value;
                const championTitle = document.getElementById('champion-title').value;
                const generationLevel = document.getElementById('generation-level').value;

                if (!bloodline) {
                    showAlert('Please select a bloodline to propagate', 'error');
                    return;
                }

                if (selectedPhotos.size === 0) {
                    showAlert('Please select photos to update with bloodline information', 'error');
                    return;
                }

                showLoading();

                // Prepare bulk update data
                const updates = Array.from(selectedPhotos).map(s3Key => ({
                    s3Key: s3Key,
                    metadata: {
                        bloodline: bloodline,
                        championTitles: championTitle,
                        generationLevel: generationLevel
                    }
                }));

                // Send bulk update request
                const result = await apiRequest('/CatManagement/photos/bulk-update', {
                    method: 'POST',
                    body: JSON.stringify(updates)
                });

                if (result.success) {
                    showAlert(`Bloodline propagated to ${selectedPhotos.size} photos successfully`, 'success');
                    selectedPhotos.clear();
                    loadTabData('bulk'); // Refresh the bulk operations tab
                } else {
                    showAlert(result.message || 'Failed to propagate bloodline', 'error');
                }

            } catch (error) {
                console.error('Error propagating bloodline:', error);
                showAlert('Error propagating bloodline', 'error');
            } finally {
                hideLoading();
            }
        }

        function validatePedigree() {
            showAlert('Pedigree validation coming soon', 'warning');
        }

        function updateFamilyRelationships() {
            showAlert('Family relationship updates coming soon', 'warning');
        }

        async function generateFamilyTree() {
            try {
                const selectedCatId = document.getElementById('pedigree-cat-select').value;

                if (!selectedCatId) {
                    showAlert('Please select a cat to generate family tree', 'error');
                    return;
                }

                showLoading();

                // Fetch family tree data
                const result = await apiRequest(`/CatManagement/family-tree/${selectedCatId}?generations=3`);

                if (result.success) {
                    displayFamilyTree(result.familyTree);
                    showAlert('Family tree generated successfully', 'success');
                } else {
                    showAlert(result.message || 'Failed to generate family tree', 'error');
                }

            } catch (error) {
                console.error('Error generating family tree:', error);
                showAlert('Error generating family tree', 'error');
            } finally {
                hideLoading();
            }
        }

        function displayFamilyTree(familyTree) {
            const display = document.getElementById('family-tree-display');

            if (!familyTree || !familyTree.cat) {
                display.innerHTML = '<p>No family tree data available for this cat.</p>';
                return;
            }

            const treeHtml = `
                <div class="family-tree-container">
                    <h3>Family Tree for ${familyTree.cat.catName}</h3>
                    <div class="tree-structure">
                        <div class="generation generation-0">
                            <div class="cat-node main-cat">
                                <strong>${familyTree.cat.catName}</strong>
                                <br><small>${familyTree.cat.breed} • ${familyTree.cat.bloodline || 'No bloodline'}</small>
                            </div>
                        </div>

                        ${familyTree.parents && familyTree.parents.length > 0 ? `
                        <div class="generation generation-1">
                            <h4>Parents</h4>
                            <div class="cat-nodes">
                                ${familyTree.parents.map(parent => `
                                    <div class="cat-node parent">
                                        <strong>${parent.catName}</strong>
                                        <br><small>${parent.gender} • ${parent.bloodline || 'No bloodline'}</small>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}

                        ${familyTree.grandparents && familyTree.grandparents.length > 0 ? `
                        <div class="generation generation-2">
                            <h4>Grandparents</h4>
                            <div class="cat-nodes">
                                ${familyTree.grandparents.map(grandparent => `
                                    <div class="cat-node grandparent">
                                        <strong>${grandparent.catName}</strong>
                                        <br><small>${grandparent.gender} • ${grandparent.bloodline || 'No bloodline'}</small>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}

                        ${familyTree.offspring && familyTree.offspring.length > 0 ? `
                        <div class="generation generation-offspring">
                            <h4>Offspring (${familyTree.offspring.length})</h4>
                            <div class="cat-nodes">
                                ${familyTree.offspring.slice(0, 6).map(offspring => `
                                    <div class="cat-node offspring">
                                        <strong>${offspring.catName}</strong>
                                        <br><small>${offspring.gender} • ${offspring.bloodline || 'No bloodline'}</small>
                                    </div>
                                `).join('')}
                                ${familyTree.offspring.length > 6 ? `<div class="more-indicator">+${familyTree.offspring.length - 6} more</div>` : ''}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;

            display.innerHTML = treeHtml;
        }

        function validateRelationships() {
            showAlert('Relationship validation coming soon', 'warning');
        }

        // Modal handling
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                e.target.closest('.modal').style.display = 'none';
            });
        });

        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            // Check for admin token
            if (!token) {
                alert('Admin authentication required. Please log in first.');
                window.location.href = 'admin.html';
                return;
            }

            // Add search event listeners
            const searchInput = document.getElementById('cat-search');
            const breedFilter = document.getElementById('breed-filter');
            const bloodlineFilter = document.getElementById('bloodline-filter');
            const statusFilter = document.getElementById('status-filter');

            if (searchInput) {
                searchInput.addEventListener('input', searchCats);
            }
            if (breedFilter) {
                breedFilter.addEventListener('change', searchCats);
            }
            if (bloodlineFilter) {
                bloodlineFilter.addEventListener('change', searchCats);
            }
            if (statusFilter) {
                statusFilter.addEventListener('change', searchCats);
            }

            // Load initial overview data
            loadOverviewData();
        });
    </script>
</body>
</html>
