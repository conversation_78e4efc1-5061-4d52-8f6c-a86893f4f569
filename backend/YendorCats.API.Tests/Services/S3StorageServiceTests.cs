using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using Amazon.S3;
using Amazon.S3.Model;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using YendorCats.API.Services;
using System.Net;

namespace YendorCats.API.Tests.Services
{
    public class S3StorageServiceTests : IDisposable
    {
        private readonly Mock<IAmazonS3> _mockS3Client;
        private readonly Mock<ILogger<S3StorageService>> _mockLogger;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly IMemoryCache _memoryCache;
        private readonly S3StorageService _s3StorageService;

        public S3StorageServiceTests()
        {
            _mockS3Client = new Mock<IAmazonS3>();
            _mockLogger = new Mock<ILogger<S3StorageService>>();
            _mockConfiguration = new Mock<IConfiguration>();
            _memoryCache = new MemoryCache(new MemoryCacheOptions());

            // Setup configuration
            _mockConfiguration.Setup(c => c["AWS:S3:BucketName"]).Returns("test-bucket");
            _mockConfiguration.Setup(c => c["AWS:S3:UseDirectS3Urls"]).Returns("true");
            _mockConfiguration.Setup(c => c["AWS:S3:PublicUrl"]).Returns("https://{bucket}.s3.amazonaws.com/{key}");
            _mockConfiguration.Setup(c => c["AWS:S3:ServiceUrl"]).Returns("https://s3.amazonaws.com");
            _mockConfiguration.Setup(c => c["AWS:S3:UseCdn"]).Returns("false");
            _mockConfiguration.Setup(c => c["AWS:S3:CdnDomain"]).Returns(string.Empty);

            _s3StorageService = new S3StorageService(_mockS3Client.Object, _mockConfiguration.Object, _mockLogger.Object, _memoryCache);
        }

        [Fact]
        public async Task GetObjectMetadataAsync_FirstCall_RetrievesFromS3AndCaches()
        {
            // Arrange
            var fileName = "test-file.jpg";
            var expectedMetadata = new Dictionary<string, string>
            {
                ["cat-id"] = "123",
                ["cat-name"] = "Fluffy"
            };

            var mockResponse = new GetObjectMetadataResponse();
            foreach (var kvp in expectedMetadata)
            {
                mockResponse.Metadata.Add(kvp.Key, kvp.Value);
            }

            _mockS3Client.Setup(s => s.GetObjectMetadataAsync(It.Is<GetObjectMetadataRequest>(r => r.Key == fileName), It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockResponse);

            // Act
            var result = await _s3StorageService.GetObjectMetadataAsync(fileName);

            // Assert
            Assert.Equal(expectedMetadata, result);
            _mockS3Client.Verify(s => s.GetObjectMetadataAsync(It.IsAny<GetObjectMetadataRequest>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetObjectMetadataAsync_SecondCall_ReturnsFromCache()
        {
            // Arrange
            var fileName = "test-file.jpg";
            var expectedMetadata = new Dictionary<string, string>
            {
                ["cat-id"] = "123",
                ["cat-name"] = "Fluffy"
            };

            var mockResponse = new GetObjectMetadataResponse();
            foreach (var kvp in expectedMetadata)
            {
                mockResponse.Metadata.Add(kvp.Key, kvp.Value);
            }

            _mockS3Client.Setup(s => s.GetObjectMetadataAsync(It.Is<GetObjectMetadataRequest>(r => r.Key == fileName), It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockResponse);

            // Act - First call
            await _s3StorageService.GetObjectMetadataAsync(fileName);
            
            // Act - Second call
            var result = await _s3StorageService.GetObjectMetadataAsync(fileName);

            // Assert
            Assert.Equal(expectedMetadata, result);
            // Should only call S3 once, second call should come from cache
            _mockS3Client.Verify(s => s.GetObjectMetadataAsync(It.IsAny<GetObjectMetadataRequest>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetObjectMetadataAsync_FileNotFound_ReturnsEmptyDictionaryAndCaches()
        {
            // Arrange
            var fileName = "nonexistent-file.jpg";
            
            _mockS3Client.Setup(s => s.GetObjectMetadataAsync(It.Is<GetObjectMetadataRequest>(r => r.Key == fileName), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new AmazonS3Exception("Not Found") { StatusCode = HttpStatusCode.NotFound });

            // Act
            var result = await _s3StorageService.GetObjectMetadataAsync(fileName);

            // Assert
            Assert.Empty(result);
            _mockS3Client.Verify(s => s.GetObjectMetadataAsync(It.IsAny<GetObjectMetadataRequest>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ListFilesAsync_FirstCall_RetrievesFromS3AndCaches()
        {
            // Arrange
            var prefix = "cats/";
            var expectedFiles = new List<S3Object>
            {
                new S3Object { Key = "cats/cat1.jpg", Size = 1024 },
                new S3Object { Key = "cats/cat2.jpg", Size = 2048 }
            };

            var mockResponse = new ListObjectsV2Response
            {
                S3Objects = expectedFiles
            };

            _mockS3Client.Setup(s => s.ListObjectsV2Async(It.Is<ListObjectsV2Request>(r => r.Prefix == prefix), It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockResponse);

            // Act
            var result = await _s3StorageService.ListFilesAsync(prefix);

            // Assert
            Assert.Equal(expectedFiles, result);
            _mockS3Client.Verify(s => s.ListObjectsV2Async(It.IsAny<ListObjectsV2Request>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ListFilesAsync_SecondCall_ReturnsFromCache()
        {
            // Arrange
            var prefix = "cats/";
            var expectedFiles = new List<S3Object>
            {
                new S3Object { Key = "cats/cat1.jpg", Size = 1024 }
            };

            var mockResponse = new ListObjectsV2Response
            {
                S3Objects = expectedFiles
            };

            _mockS3Client.Setup(s => s.ListObjectsV2Async(It.Is<ListObjectsV2Request>(r => r.Prefix == prefix), It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockResponse);

            // Act - First call
            await _s3StorageService.ListFilesAsync(prefix);
            
            // Act - Second call
            var result = await _s3StorageService.ListFilesAsync(prefix);

            // Assert
            Assert.Equal(expectedFiles, result);
            // Should only call S3 once, second call should come from cache
            _mockS3Client.Verify(s => s.ListObjectsV2Async(It.IsAny<ListObjectsV2Request>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task UpdateObjectMetadataAsync_Success_InvalidatesCache()
        {
            // Arrange
            var fileName = "test-file.jpg";
            var metadata = new Dictionary<string, string>
            {
                ["cat-id"] = "456",
                ["cat-name"] = "Updated Fluffy"
            };

            // Setup mocks for getting existing object
            var mockGetResponse = new GetObjectResponse();
            mockGetResponse.Headers.ContentType = "image/jpeg";
            
            _mockS3Client.Setup(s => s.GetObjectAsync(It.Is<GetObjectRequest>(r => r.Key == fileName), It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockGetResponse);

            _mockS3Client.Setup(s => s.CopyObjectAsync(It.IsAny<CopyObjectRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new CopyObjectResponse());

            // Pre-populate cache
            var cacheKey = $"s3_metadata_{fileName}";
            var oldMetadata = new Dictionary<string, string> { ["cat-id"] = "123" };
            _memoryCache.Set(cacheKey, oldMetadata);

            // Act
            await _s3StorageService.UpdateObjectMetadataAsync(fileName, metadata);

            // Assert
            _mockS3Client.Verify(s => s.CopyObjectAsync(It.IsAny<CopyObjectRequest>(), It.IsAny<CancellationToken>()), Times.Once);
            
            // Verify cache was invalidated
            Assert.False(_memoryCache.TryGetValue(cacheKey, out _));
        }

        [Fact]
        public async Task DeleteFileAsync_Success_InvalidatesCache()
        {
            // Arrange
            var fileName = "test-file.jpg";

            _mockS3Client.Setup(s => s.DeleteObjectAsync(It.Is<DeleteObjectRequest>(r => r.Key == fileName), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new DeleteObjectResponse());

            // Pre-populate cache
            var cacheKey = $"s3_metadata_{fileName}";
            var metadata = new Dictionary<string, string> { ["cat-id"] = "123" };
            _memoryCache.Set(cacheKey, metadata);

            // Act
            await _s3StorageService.DeleteFileAsync(fileName);

            // Assert
            _mockS3Client.Verify(s => s.DeleteObjectAsync(It.IsAny<DeleteObjectRequest>(), It.IsAny<CancellationToken>()), Times.Once);
            
            // Verify cache was invalidated
            Assert.False(_memoryCache.TryGetValue(cacheKey, out _));
        }

        [Fact]
        public async Task SearchByMetadataAsync_ReturnsMatchingObjects()
        {
            // Arrange
            var prefix = "cats/";
            var searchFilters = new Dictionary<string, string>
            {
                ["cat-id"] = "123"
            };

            var allFiles = new List<S3Object>
            {
                new S3Object { Key = "cats/cat1.jpg", Size = 1024 },
                new S3Object { Key = "cats/cat2.jpg", Size = 2048 }
            };

            var mockListResponse = new ListObjectsV2Response
            {
                S3Objects = allFiles
            };

            _mockS3Client.Setup(s => s.ListObjectsV2Async(It.IsAny<ListObjectsV2Request>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockListResponse);

            // Setup metadata responses
            var metadata1 = new GetObjectMetadataResponse();
            metadata1.Metadata.Add("cat-id", "123");
            metadata1.Metadata.Add("cat-name", "Fluffy");

            var metadata2 = new GetObjectMetadataResponse();
            metadata2.Metadata.Add("cat-id", "456");
            metadata2.Metadata.Add("cat-name", "Whiskers");

            _mockS3Client.Setup(s => s.GetObjectMetadataAsync(It.Is<GetObjectMetadataRequest>(r => r.Key == "cats/cat1.jpg"), It.IsAny<CancellationToken>()))
                .ReturnsAsync(metadata1);

            _mockS3Client.Setup(s => s.GetObjectMetadataAsync(It.Is<GetObjectMetadataRequest>(r => r.Key == "cats/cat2.jpg"), It.IsAny<CancellationToken>()))
                .ReturnsAsync(metadata2);

            // Act
            var result = await _s3StorageService.SearchByMetadataAsync(searchFilters, prefix);

            // Assert
            Assert.Single(result);
            Assert.Equal("cats/cat1.jpg", result[0].S3Object.Key);
            Assert.Equal("123", result[0].Metadata["cat-id"]);
            Assert.Equal("Fluffy", result[0].Metadata["cat-name"]);
        }

        [Fact]
        public async Task UploadFileWithMetadataAsync_Success_InvalidatesFileListCache()
        {
            // Arrange
            var fileName = "new-cat.jpg";
            var contentType = "image/jpeg";
            var metadata = new Dictionary<string, string>
            {
                ["cat-id"] = "789",
                ["cat-name"] = "Mittens"
            };

            using var stream = new MemoryStream(new byte[] { 1, 2, 3, 4 });

            _mockS3Client.Setup(s => s.PutObjectAsync(It.IsAny<PutObjectRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new PutObjectResponse());

            // Pre-populate file list cache
            var filesCacheKey = "s3_files_cats/";
            var cachedFiles = new List<S3Object> { new S3Object { Key = "cats/old-cat.jpg" } };
            _memoryCache.Set(filesCacheKey, cachedFiles);

            // Act
            var result = await _s3StorageService.UploadFileWithMetadataAsync(stream, fileName, contentType, metadata);

            // Assert
            Assert.NotNull(result);
            _mockS3Client.Verify(s => s.PutObjectAsync(It.IsAny<PutObjectRequest>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetObjectMetadataAsync_S3Exception_ThrowsException()
        {
            // Arrange
            var fileName = "error-file.jpg";
            
            _mockS3Client.Setup(s => s.GetObjectMetadataAsync(It.Is<GetObjectMetadataRequest>(r => r.Key == fileName), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new AmazonS3Exception("Internal Server Error") { StatusCode = HttpStatusCode.InternalServerError });

            // Act & Assert
            await Assert.ThrowsAsync<AmazonS3Exception>(() => _s3StorageService.GetObjectMetadataAsync(fileName));
        }

        [Fact]
        public async Task UpdateObjectMetadataAsync_GetObjectFails_ThrowsException()
        {
            // Arrange
            var fileName = "test-file.jpg";
            var metadata = new Dictionary<string, string> { ["cat-id"] = "123" };

            _mockS3Client.Setup(s => s.GetObjectAsync(It.Is<GetObjectRequest>(r => r.Key == fileName), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new AmazonS3Exception("Not Found") { StatusCode = HttpStatusCode.NotFound });

            // Act & Assert
            await Assert.ThrowsAsync<AmazonS3Exception>(() => _s3StorageService.UpdateObjectMetadataAsync(fileName, metadata));
        }

        [Fact]
        public async Task GetS3ConfigurationAsync_ReturnsConfiguration()
        {
            // Arrange
            _mockS3Client.Setup(s => s.GetBucketLocationAsync(It.IsAny<GetBucketLocationRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new GetBucketLocationResponse { Location = S3Region.USEast1 });

            // Act
            var result = await _s3StorageService.GetS3ConfigurationAsync();

            // Assert
            Assert.Equal("test-bucket", result["bucketName"]);
            Assert.Equal(false, result["useCdn"]);
            Assert.Equal(true, result["useDirectS3Urls"]);
            Assert.Equal("https://s3.amazonaws.com", result["serviceUrl"]);
        }

        public void Dispose()
        {
            _memoryCache?.Dispose();
        }
    }
}