using Microsoft.EntityFrameworkCore;
using YendorCats.API.Models;

namespace YendorCats.API.Data
{
    /// <summary>
    /// Database context for the Yendor Cats application
    /// </summary>
    public class AppDbContext : DbContext
    {
        /// <summary>
        /// Constructor for the database context
        /// </summary>
        /// <param name="options">The DbContext options</param>
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        /// <summary>
        /// Collection of cats in the database
        /// </summary>
        public DbSet<Cat> Cats { get; set; } = null!;
        
        /// <summary>
        /// Collection of cat images in the database
        /// </summary>
        public DbSet<CatImage> CatImages { get; set; } = null!;

        /// <summary>
        /// Collection of cat gallery images with metadata
        /// </summary>
        public DbSet<CatGalleryImage> CatGalleryImages { get; set; } = null!;

        /// <summary>
        /// Collection of users in the database
        /// </summary>
        public DbSet<User> Users { get; set; } = null!;

        /// <summary>
        /// Collection of admin users in the database
        /// </summary>
        public DbSet<AdminUser> AdminUsers { get; set; } = null!;

        /// <summary>
        /// Collection of clients in the database
        /// </summary>
        public DbSet<Client> Clients { get; set; } = null!;

        /// <summary>
        /// Collection of appointments in the database
        /// </summary>
        public DbSet<Appointment> Appointments { get; set; } = null!;

        /// <summary>
        /// Configure the database model
        /// </summary>
        /// <param name="modelBuilder">The model builder</param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Cat entity
            modelBuilder.Entity<Cat>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Breed).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Gender).IsRequired().HasMaxLength(1);
                entity.Property(e => e.Color).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Markings).HasMaxLength(200);
                
                // Configure relationships
                entity.HasOne(e => e.Mother)
                    .WithMany()
                    .HasForeignKey(e => e.MotherId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired(false);
                
                entity.HasOne(e => e.Father)
                    .WithMany()
                    .HasForeignKey(e => e.FatherId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired(false);
            });

            // Configure CatImage entity
            modelBuilder.Entity<CatImage>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FilePath).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.ContentType).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Caption).HasMaxLength(500);

                // Configure relationships
                entity.HasOne(e => e.Cat)
                    .WithMany(c => c.Images)
                    .HasForeignKey(e => e.CatId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure CatGalleryImage entity
            modelBuilder.Entity<CatGalleryImage>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.CatName).HasMaxLength(100);
                entity.Property(e => e.ImageUrl).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.Category).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Color).HasMaxLength(100);
                entity.Property(e => e.Gender).HasMaxLength(10);
                entity.Property(e => e.Traits).HasMaxLength(500);
                entity.Property(e => e.Mother).HasMaxLength(100);
                entity.Property(e => e.Father).HasMaxLength(100);
                entity.Property(e => e.Bloodline).HasMaxLength(200);
                entity.Property(e => e.Breed).HasMaxLength(100);
                entity.Property(e => e.Personality).HasMaxLength(500);
                entity.Property(e => e.Tags).HasMaxLength(500);
                entity.Property(e => e.FileFormat).HasMaxLength(20);

                // Create index for faster category queries
                entity.HasIndex(e => e.Category);
                entity.HasIndex(e => e.CatName);
            });

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
                entity.Property(e => e.FirstName).HasMaxLength(50);
                entity.Property(e => e.LastName).HasMaxLength(50);
                entity.Property(e => e.PasswordHash).IsRequired();
                entity.Property(e => e.PasswordSalt).IsRequired();
                entity.Property(e => e.Role).IsRequired().HasMaxLength(20);
                
                // Create unique index for username
                entity.HasIndex(e => e.Username).IsUnique();
                
                // Create unique index for email
                entity.HasIndex(e => e.Email).IsUnique();
            });

            // Configure AdminUser entity
            modelBuilder.Entity<AdminUser>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PasswordHash).IsRequired();
                entity.Property(e => e.PasswordSalt).IsRequired();
                entity.Property(e => e.Role).IsRequired().HasMaxLength(20);

                // Create unique indexes
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
            });

            // Configure Client entity
            modelBuilder.Entity<Client>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(50);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Phone).HasMaxLength(20);
                entity.Property(e => e.Address).HasMaxLength(200);
                entity.Property(e => e.City).HasMaxLength(50);
                entity.Property(e => e.State).HasMaxLength(50);
                entity.Property(e => e.PostalCode).HasMaxLength(20);
                entity.Property(e => e.Country).HasMaxLength(50);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                
                // Create index for email
                entity.HasIndex(e => e.Email);
                
                // Create index for name searching
                entity.HasIndex(e => e.LastName);
                entity.HasIndex(e => e.FirstName);
            });

            // Configure Appointment entity
            modelBuilder.Entity<Appointment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Location).HasMaxLength(200);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                
                // Configure relationships
                entity.HasOne(e => e.Client)
                    .WithMany(c => c.Appointments)
                    .HasForeignKey(e => e.ClientId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.Cat)
                    .WithMany()
                    .HasForeignKey(e => e.CatId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .IsRequired(false);
                
                // Create indexes for querying
                entity.HasIndex(e => e.StartTime);
                entity.HasIndex(e => e.ClientId);
                entity.HasIndex(e => e.Status);
            });
        }
    }
}
