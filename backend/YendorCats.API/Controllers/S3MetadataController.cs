using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Attributes;
using YendorCats.API.Services;
using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for managing S3 object metadata
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [AdminAuthorize("SuperAdmin", "Admin", "Editor")]
    public class S3MetadataController : ControllerBase
    {
        private readonly IS3StorageService _s3StorageService;
        private readonly ILogger<S3MetadataController> _logger;

        public S3MetadataController(
            IS3StorageService s3StorageService,
            ILogger<S3MetadataController> logger)
        {
            _s3StorageService = s3StorageService;
            _logger = logger;
        }

        /// <summary>
        /// Update metadata for an S3 object
        /// </summary>
        /// <param name="request">Metadata update request</param>
        /// <returns>Success response</returns>
        [HttpPost("update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateMetadata([FromBody] S3MetadataUpdateRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} updating metadata for S3 key: {S3Key}", 
                    adminUser?.Username, request.S3Key);

                // Create CatImageMetadata object from request
                var catImageMetadata = new Models.CatImageMetadata
                {
                    Name = request.CatName ?? string.Empty,
                    Age = request.Age,
                    DateTaken = !string.IsNullOrEmpty(request.DateTaken) && DateTime.TryParse(request.DateTaken, out var dateTaken) ? dateTaken : null,
                    Description = request.Description,
                    Breed = request.Breed,
                    Gender = request.Gender ?? string.Empty,
                    HairColor = request.Color,
                    Personality = request.Personality,
                    Bloodline = request.Bloodline,
                    CatId = !string.IsNullOrEmpty(request.CatId) && int.TryParse(request.CatId, out var catId) ? catId : null,
                    RegisteredName = request.RegisteredName,
                    RegistrationNumber = request.RegistrationNumber,
                    FatherCatId = !string.IsNullOrEmpty(request.FatherId) && int.TryParse(request.FatherId, out var fatherId) ? fatherId : null,
                    MotherCatId = !string.IsNullOrEmpty(request.MotherId) && int.TryParse(request.MotherId, out var motherId) ? motherId : null,
                    BreedingStatus = request.BreedingStatus,
                    AvailabilityStatus = request.AvailabilityStatus,
                    PhotoType = request.PhotoType,
                    AgeAtPhoto = request.AgeAtPhoto,
                    Tags = request.Tags,
                    ChampionTitles = request.ChampionTitles,
                    GenerationLevel = request.GenerationLevel,
                    DateUploaded = DateTime.UtcNow,
                    FileFormat = "jpg", // Default, should be determined from S3 object
                    ContentType = "image/jpeg" // Default, should be determined from S3 object
                };

                // Convert to S3 metadata format using the model's method
                var metadata = catImageMetadata.ToS3Metadata();
                // Add system metadata
                metadata["updated-by"] = adminUser?.Username ?? "system";
                metadata["updated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");

                // Update S3 object metadata by copying to itself with new metadata
                await UpdateS3ObjectMetadata(request.S3Key, metadata);

                _logger.LogInformation("Successfully updated metadata for S3 key: {S3Key}", request.S3Key);

                return Ok(new { 
                    success = true, 
                    message = "Metadata updated successfully",
                    s3Key = request.S3Key,
                    updatedBy = adminUser?.Username,
                    updatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating metadata for S3 key: {S3Key}", request.S3Key);
                return StatusCode(500, new { 
                    success = false, 
                    message = "An error occurred while updating metadata" 
                });
            }
        }

        /// <summary>
        /// Get current metadata for an S3 object
        /// </summary>
        /// <param name="s3Key">S3 object key</param>
        /// <returns>Current metadata</returns>
        [HttpGet("get/{*s3Key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetMetadata(string s3Key)
        {
            try
            {
                var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Key);

                return Ok(new {
                    s3Key = s3Key,
                    metadata = metadata,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving metadata for S3 key: {S3Key}", s3Key);
                return StatusCode(500, new { 
                    success = false, 
                    message = "An error occurred while retrieving metadata" 
                });
            }
        }

        /// <summary>
        /// Bulk update metadata for multiple S3 objects
        /// </summary>
        /// <param name="requests">List of metadata update requests</param>
        /// <returns>Bulk update results</returns>
        [HttpPost("bulk-update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> BulkUpdateMetadata([FromBody] List<S3MetadataUpdateRequest> requests)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                var results = new List<object>();

                _logger.LogInformation("Admin {Username} performing bulk metadata update for {Count} objects", 
                    adminUser?.Username, requests.Count);

                foreach (var request in requests)
                {
                    try
                    {
                        // Create CatImageMetadata object from request
                        var catImageMetadata = new Models.CatImageMetadata
                        {
                            Name = request.CatName ?? string.Empty,
                            Age = request.Age,
                            DateTaken = !string.IsNullOrEmpty(request.DateTaken) && DateTime.TryParse(request.DateTaken, out var dateTaken) ? dateTaken : null,
                            Description = request.Description,
                            Breed = request.Breed,
                            Gender = request.Gender ?? string.Empty,
                            HairColor = request.Color,
                            Personality = request.Personality,
                            Bloodline = request.Bloodline,
                            CatId = !string.IsNullOrEmpty(request.CatId) && int.TryParse(request.CatId, out var catId) ? catId : null,
                            RegisteredName = request.RegisteredName,
                            RegistrationNumber = request.RegistrationNumber,
                            FatherCatId = !string.IsNullOrEmpty(request.FatherId) && int.TryParse(request.FatherId, out var fatherId) ? fatherId : null,
                            MotherCatId = !string.IsNullOrEmpty(request.MotherId) && int.TryParse(request.MotherId, out var motherId) ? motherId : null,
                            BreedingStatus = request.BreedingStatus,
                            AvailabilityStatus = request.AvailabilityStatus,
                            PhotoType = request.PhotoType,
                            AgeAtPhoto = request.AgeAtPhoto,
                            Tags = request.Tags,
                            ChampionTitles = request.ChampionTitles,
                            GenerationLevel = request.GenerationLevel,
                            DateUploaded = DateTime.UtcNow,
                            FileFormat = "jpg", // Default, should be determined from S3 object
                            ContentType = "image/jpeg" // Default, should be determined from S3 object
                        };

                        // Convert to S3 metadata format using the model's method
                        var metadata = catImageMetadata.ToS3Metadata();
                        
                        // Add system metadata
                        metadata["updated-by"] = adminUser?.Username ?? "system";
                        metadata["updated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");

                        await UpdateS3ObjectMetadata(request.S3Key, metadata);

                        results.Add(new { 
                            s3Key = request.S3Key, 
                            success = true, 
                            message = "Updated successfully" 
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error updating metadata for S3 key: {S3Key}", request.S3Key);
                        results.Add(new { 
                            s3Key = request.S3Key, 
                            success = false, 
                            message = ex.Message 
                        });
                    }

                    // Small delay to avoid rate limiting
                    await Task.Delay(100);
                }

                var successCount = results.Count(r => (bool)r.GetType().GetProperty("success")?.GetValue(r)!);

                return Ok(new {
                    success = true,
                    message = $"Bulk update completed: {successCount}/{requests.Count} successful",
                    results = results,
                    updatedBy = adminUser?.Username,
                    completedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during bulk metadata update");
                return StatusCode(500, new { 
                    success = false, 
                    message = "An error occurred during bulk update" 
                });
            }
        }

        private async Task UpdateS3ObjectMetadata(string s3Key, Dictionary<string, string> metadata)
        {
            await _s3StorageService.UpdateObjectMetadataAsync(s3Key, metadata);
        }
    }

    /// <summary>
    /// S3 metadata update request model
    /// </summary>
    public class S3MetadataUpdateRequest
    {
        /// <summary>
        /// S3 object key
        /// </summary>
        [Required(ErrorMessage = "S3 object key is required")]
        [StringLength(1024, ErrorMessage = "S3 key cannot exceed 1024 characters")]
        public string S3Key { get; set; } = string.Empty;

        /// <summary>
        /// Cat name
        /// </summary>
        [StringLength(100, ErrorMessage = "Cat name cannot exceed 100 characters")]
        public string? CatName { get; set; }

        /// <summary>
        /// Cat age
        /// </summary>
        [StringLength(50, ErrorMessage = "Age cannot exceed 50 characters")]
        [RegularExpression(@"^[\d\.\-\s]*$", ErrorMessage = "Age must contain only numbers, dots, dashes, and spaces")]
        public string? Age { get; set; }

        /// <summary>
        /// Date photo was taken
        /// </summary>
        [DataType(DataType.Date)]
        public string? DateTaken { get; set; }

        /// <summary>
        /// Photo description
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Cat breed
        /// </summary>
        [StringLength(100, ErrorMessage = "Breed cannot exceed 100 characters")]
        public string? Breed { get; set; }

        /// <summary>
        /// Cat gender
        /// </summary>
        [StringLength(10, ErrorMessage = "Gender cannot exceed 10 characters")]
        [RegularExpression(@"^[MF]$", ErrorMessage = "Gender must be 'M' or 'F'")]
        public string? Gender { get; set; }

        /// <summary>
        /// Cat color/pattern
        /// </summary>
        [StringLength(100, ErrorMessage = "Color cannot exceed 100 characters")]
        public string? Color { get; set; }

        /// <summary>
        /// Cat personality
        /// </summary>
        [StringLength(500, ErrorMessage = "Personality cannot exceed 500 characters")]
        public string? Personality { get; set; }

        /// <summary>
        /// Cat bloodline
        /// </summary>
        [StringLength(100, ErrorMessage = "Bloodline cannot exceed 100 characters")]
        public string? Bloodline { get; set; }

        /// <summary>
        /// Unique cat identifier
        /// </summary>
        [StringLength(50, ErrorMessage = "Cat ID cannot exceed 50 characters")]
        [RegularExpression(@"^[a-zA-Z0-9\-_]*$", ErrorMessage = "Cat ID can only contain letters, numbers, hyphens, and underscores")]
        public string? CatId { get; set; }

        /// <summary>
        /// Official registered name
        /// </summary>
        [StringLength(200, ErrorMessage = "Registered name cannot exceed 200 characters")]
        public string? RegisteredName { get; set; }

        /// <summary>
        /// Registration number from pedigree papers
        /// </summary>
        [StringLength(100, ErrorMessage = "Registration number cannot exceed 100 characters")]
        public string? RegistrationNumber { get; set; }

        /// <summary>
        /// Father's cat ID
        /// </summary>
        [StringLength(50, ErrorMessage = "Father ID cannot exceed 50 characters")]
        [RegularExpression(@"^[a-zA-Z0-9\-_]*$", ErrorMessage = "Father ID can only contain letters, numbers, hyphens, and underscores")]
        public string? FatherId { get; set; }

        /// <summary>
        /// Mother's cat ID
        /// </summary>
        [StringLength(50, ErrorMessage = "Mother ID cannot exceed 50 characters")]
        [RegularExpression(@"^[a-zA-Z0-9\-_]*$", ErrorMessage = "Mother ID can only contain letters, numbers, hyphens, and underscores")]
        public string? MotherId { get; set; }

        /// <summary>
        /// Breeding status (available-kitten, breeding-queen, stud, retired)
        /// </summary>
        [StringLength(50, ErrorMessage = "Breeding status cannot exceed 50 characters")]
        [RegularExpression(@"^(available-kitten|breeding-queen|stud|retired)$", ErrorMessage = "Breeding status must be one of: available-kitten, breeding-queen, stud, retired")]
        public string? BreedingStatus { get; set; }

        /// <summary>
        /// Availability status (available, reserved, sold, not-for-sale)
        /// </summary>
        [StringLength(50, ErrorMessage = "Availability status cannot exceed 50 characters")]
        [RegularExpression(@"^(available|reserved|sold|not-for-sale)$", ErrorMessage = "Availability status must be one of: available, reserved, sold, not-for-sale")]
        public string? AvailabilityStatus { get; set; }

        /// <summary>
        /// Type of photo (profile, action, family, breeding, growth)
        /// </summary>
        [StringLength(50, ErrorMessage = "Photo type cannot exceed 50 characters")]
        [RegularExpression(@"^(profile|action|family|breeding|growth)$", ErrorMessage = "Photo type must be one of: profile, action, family, breeding, growth")]
        public string? PhotoType { get; set; }

        /// <summary>
        /// Age when photo was taken
        /// </summary>
        [StringLength(50, ErrorMessage = "Age at photo cannot exceed 50 characters")]
        public string? AgeAtPhoto { get; set; }

        /// <summary>
        /// Searchable tags (comma-separated)
        /// </summary>
        [StringLength(500, ErrorMessage = "Tags cannot exceed 500 characters")]
        public string? Tags { get; set; }

        /// <summary>
        /// Champion titles and achievements
        /// </summary>
        [StringLength(300, ErrorMessage = "Champion titles cannot exceed 300 characters")]
        public string? ChampionTitles { get; set; }

        /// <summary>
        /// Generation level in pedigree (1=parent, 2=grandparent, etc.)
        /// </summary>
        [StringLength(10, ErrorMessage = "Generation level cannot exceed 10 characters")]
        [RegularExpression(@"^[1-9]$", ErrorMessage = "Generation level must be a number between 1 and 9")]
        public string? GenerationLevel { get; set; }
    }
}
