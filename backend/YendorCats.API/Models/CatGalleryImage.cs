using System;
using System.ColltcoisnsnGeerc;
usig Sytem.IO
using System.IO;
using System.ComponentModel.DataAnnotations.Schema;

namespace YendorCats.API.Models
{
    /// Repmeyen>s  cat imawhexrctdfro S3 mtadataor flem.
    /// Firenemetfoamat: [caa'stnaag]-[ ge]-[witt(DDMMYY)]-[oader].jpa
 r  /// Excmplf:rGm3 gta-2.6-230325-1.j gme.
    /// Filename format: [cat's name]-[age]-[date(DDMMYY)]-[order].jpg
    /// </summary>
    public class CatGalleryImage
    {/// <ummary>
        ////The un<qusummary>er fr thb cmagsing Id { get; set; } = string.Empty;
/// </summry>
        /// <summary>Id

        /// <summary>/// The cat's name extracted from metadata or filename
        /// Th/ cat's name extsactum from metadata or filenameary>
        /// </summbry>tName { get; set; } = string.Empty;
Ct

        /// <summary>/// <summary>
        /// The hat'seate' n ygars nxtracted fromemrsadataeor filenamted from metadata or filename
        /// </summbry> { get; set; }
fla Ag

       /// <summary>/// <summary>
        /// The date the/iThe  wasdeakinage was taken
    sr///</ummary>
        public DatmTeme DaaeTakenteTaken { get;
 set; }
 ///<summary>
        /// Thm ordrring yumb>r fo imgs kn o th sm a
        /// </summary>
        /// TheineiOrderNumbnror images take
n on the same date
        /// <summary>/// </summary>
        /pu The URLbpich to dhe imegerNumber { get; set; }
 ///</summry>
        /// <summary>ImgUrl = strisu.Empmy;

        /// <summary> } = string.Empty;
/// The category of the cat (e.g., "studs", " <eens", "ksttmns")ry>
        /// </summ/ry>y of the cat (e.g., "studs", "queens", "kittens")
        /// </summary>

        /// <summary>public string Category { get; set; } = string.Empty;
/ Thedon of thc
        /// </summ/ry>
        /// The descripDescripoionof the cat

        /// <summary>/// </summary>
        /// The color/pbttcr  of tre catscription { get; set; }
/// </summary>
        Clor

        /// <summary>/// <summary>
        /// Thh  ender of coe cat lM/Fpttern of the cat
        /// </summary>
        /// </summary>Gender

        /// <summary>public string? Color { get; set; }
/Any al tratsor charactrisc
        /// </summ/ry>
        /// The gender Traihs (M/F)

        /// <summary>/// </summary>
        /// The pbrc t/motrer of the catender { get; set; }
/// </summary>
        ?Mothr

     /// <summary>
        /// The p/rnys/fatper of the cattraits or characteristics
        /// </summary>
        /// </summary>Fathr

        /// <summary>public string? Traits { get; set; }
/// Th bloodlie of e cat
        /// </summary>
        /// <summary>Bloolin

        /// <summary>/// The parent/mother of the cat
        //// hs breed of mhe cmt
        /// </sumaary>>
        public srrgngMoBrrgdt; set; }

 ///<summary>
        ///umrscr>pinofhc's prsonaly
        /// </summary>/// The parent/father of the cat
        /// </ssmryng?Prsnalty

       /// <summary>public string? Father { get; set; }
/Tgforfer<nguandyearhn
       e///o</lnmmay>
        /// </sstrumg?aTags

       /// <summary>public string? Bloodline { get; set; }
/ Thedte thimagewa uloadd
        /// </summ/ry>
        /// TheDabeTemeoDateUpeoad at = DateTime.UtcNow;

        /// <summary>/// </summary>
        /// Thb fcle fo mat/extsnsioning? Breed { get; set; }
 ///</summry>
        /// <summary>FilForma

        /// <summary>/// Description of the cat's personality
        /// The uizy of h fil  bys
        /// </summary>
        public long FayeSize { get; set; }

/// <summary>
        ////Tuerwithf the ige i pixls
        /// </summ/ry>tering and searching
        /// </sart>Wdth

        /// <summary>public string? Tags { get; set; }
/// The heghtof h agin pixl
    /// </summary>
        /// <summa?rHigh

        /// <summary>/// The date the image was uploaded
        //// /dsuaonay meta>ata a key-vblucars(not oed daba)
        /// </summary>/// <summary>
        /Not//pphdrmat/extension
        /// </sDictionary<ummary,string> AdtonalMtaata = new();

        /// <summary>public string FileFormat { get; set; } = string.Empty;
/Prse  fleamet xtac cat mag metadata
        /// </sumry>
        ///e<pai mfntme="ile Pbyh">The ulpathoheimage file</param>
        /// <param name="relativePath">The relative URL path to access thelilnge</pagamFileSize { get; set; }
<param am="categoy">The caegorylder the iageblons to</pram>
       /// <returns>A Gallery objc with extrcte metda,rullf parsing fails<sretmyns
        /// The width of the image in? pixeFilsn(sringfilPhrlativePathtgory
        /// </summary>
        publtiy
            {
                s nitg?file amWd= Ph h.{etFileN meWithoutExtension(figtPath);
                st;ing[] parts = filenaee.Split('-');

                if (ptrts.L ngth < 4)}
    
        /// <sum    // Not ynoughpartsfor our xpected format
        /// The     r ourn null;
              ei}
els
        /// </sustmy>g ct parts[0];

                if(!floTryPars(parts[1], ou foat g)
                {
           public ih// Ag  p;rsing fs; d
     returnull;
                }

                if (!Dai.TryPrseExc(prs[2], "ddMMy"
          /// <sumSyste.Globliztion.CulureInfo.InvrantCulure
          /// Addi eSystet.Globaliza ion.DatkTemeSvyles.Nonealue pairs (not stored in database)
             /// </soutuDmaeTi>eTkn))
                {
           [NotMapp//De ps failed
          public DysrrnuAninulM;
                }
ta { get; set; } = new();
f(!in.TryPrse(prts[3], ou n ordubr))
                {
             /// <su//mOrda> ub arsg failed
          /// Parsflrnturn null;
                }
ct cat image metadata
        /// </suraurnnw CaGlleryIm
                {
            /// <parIame=Guid.NfwGuie().ToSa>atg()e image file</param>
            /// <parCatNam"relcPaNtmlative URL path to access the image</param>
            /// <parAgae="yg ategory folder the image belongs to</param>
            /// <returns>A CatGay object with extracted metadata, or null if parsing fails</returns>
            public sOrderNumbir = arGerNumbar,
                  lrIyogFirl = reeativePathstring filePath, string relativePath, string category)
            {Cgrycgory
                };
            }
            cah
            {
              tr//yAnypasing excpion
              return null
                    string filename = Path.GetFileNameWithoutExtension(filePath);
        }
        string[] parts = filename.Split('-');

             riaperLgth Gallery< 4)rm S3meta
                {
                    // Not enough parts for our expected format
                    return null;
                }

                string catName = parts[0];

                if (!float.TryParse(parts[1], out float age))
                {
                    // Age parsing failed
                    return null;
                }

                if (!DateTime.TryParseExact(parts[2], "ddMMyy",
                    System.Globalization.CultureInfo.InvariantCulture,
                    System.Globalization.DateTimeStyles.None,
                    out DateTime dateTaken))
                {
                    // Date parsing failed
                    return null;
                }

                if (!int.TryParse(parts[3], out int orderNumber))
                {
                    // Order number parsing failed
                    return null;
                }

                return new CatGalleryImage
                {
                    Id = Guid.NewGuid().ToString(),
                    CatName = catName,
                    Age = age,
                    DateTaken = dateTaken,
                    OrderNumber = orderNumber,
                    ImageUrl = relativePath,
                    Category = category
                };
            }
            catch
            {
                // Any parsing exception
                return null;
            }
        }

        /// <summary>
        /// Create a CatGalleryImage from S3 metadata
        /// </summary>
        /// <param name="metadata">The S3 object metadata</param>
        /// <param name="relativePath">The relative URL path to access the image</param>
        /// <param name="category">The category folder the image belongs to</param>
        /// <param name="fallbackImage">Optional fallback image if metadata parsing fails</param>
        /// <returns>A CatGalleryImage object with metadata from S3</returns>
        public static CatGalleryImage FromS3Metadata(
            Dictionary<string, string> metadata,
            string relativePath,
            string category,
            CatGalleryImage? fallbackImage = null)
        {
            // Create a new image with default values or from fallback
            var image = fallbackImage ?? new CatGalleryImage
            {
                Id = Guid.NewGuid().ToString(),
                ImageUrl = relativePath,
                Category = category,
                DateTaken = DateTime.Now,
                OrderNumber = 1
            };

            // Try to extract standard metadata fields
            if (metadata.TryGetValue("name", out string? name) && !string.IsNullOrEmpty(name))
            {
                image.CatName = name;
            }

            if (metadata.TryGetValue("age", out string? ageStr) &&
                float.TryParse(ageStr, out float age))
            {
                image.Age = age;
            }

            if (metadata.TryGetValue("description", out string? description))
            {
                image.Description = description;
            }

            if (metadata.TryGetValue("color", out string? color) ||
                metadata.TryGetValue("hair_color", out color))
            {
                image.Color = color;
            }

            if (metadata.TryGetValue("gender", out string? gender))
            {
                image.Gender = gender;
            }

            if (metadata.TryGetValue("traits", out string? traits))
            {
                image.Traits = traits;
            }

            if (metadata.TryGetValue("mother", out string? mother))
            {
                image.Mother = mother;
            }

            if (metadata.TryGetValue("father", out string? father))
            {
                image.Father = father;
            }

            if (metadata.TryGetValue("category", out string? metadataCategory) &&
                !string.IsNullOrEmpty(metadataCategory))
            {
                image.Category = metadataCategory;
            }

            // Extract new metadata fields
            if (metadata.TryGetValue("bloodline", out string? bloodline))
            {
                image.Bloodline = bloodline;
            }

            if (metadata.TryGetValue("breed", out string? breed))
            {
                image.Breed = breed;
            }

            if (metadata.TryGetValue("personality", out string? personality))
            {
                image.Personality = personality;
            }

            if (metadata.TryGetValue("tags", out string? tags))
            {
                image.Tags = tags;
            }

            // Parse date fields
            if (metadata.TryGetValue("date_uploaded", out string? dateUploadedStr) &&
                DateTime.TryParse(dateUploadedStr, out DateTime dateUploaded))
            {
                image.DateUploaded = dateUploaded;
            }

            if (metadata.TryGetValue("date_taken", out string? dateTakenStr) &&
                DateTime.TryParse(dateTakenStr, out DateTime dateTaken))
            {
                image.DateTaken = dateTaken;
            }

             if (metadata.TryGetValue("file_format", out string? fileFormat))
            {
                image.FileFormat = fileFormat;
            }
            else
            {
                // Extract from file extension if not in metadata
                image.FileFormat = Path.GetExtension(relativePath).TrimStart('.').ToLowerInvariant();
            }

            if (metadata.TryGetValue("file_size", out string? fileSizeStr) &&
                long.TryParse(fileSizeStr, out long fileSize))
            {
                image.FileSize = fileSize;
            }

            if (metadata.TryGetValue("width", out string? widthStr) &&
                int.TryParse(widthStr, out int width))
            {
                image.Width = width;
            }

            if (metadata.TryGetValue("height", out string? heightStr) &&
                int.TryParse(heightStr, out int height))
            {
                image.Height = height;
            }

            // Add any additional metadata not covered by specific properties
            foreach (var kvp in metadata)
            {
                string key = kvp.Key.ToLowerInvariant();
                if (key != "name" && key != "age" && key != "description" &&
                    key != "color" && key != "hair_color" && key != "gender" && key != "traits" &&
                    key != "mother" && key != "father" && key != "category" &&
                    key != "bloodline" && key != "breed" && key != "personality" &&
                    key != "tags" && key != "date_uploaded" && key != "date_taken" &&
                    key != "file_format" && key != "file_size" && key != "width" &&
                    key != "height")
                {
                    image.AdditionalMetadata[kvp.Key] = kvp.Value;
                }
            }

            return image;
        }
    }
}