using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.IO;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Hybrid storage entity - metadata in database, images in S3/B2
    /// Replaces direct S3 metadata querying for 85-90% performance improvement
    /// </summary>
    [Table("CatGalleryImages")]
    public class CatGalleryImage
    {
        [Key]
        public long Id { get; set; }
        
        // File identification - CRITICAL for dual storage integration
        [Required]
        [MaxLength(255)]
        public string Filename { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(500)]
        [Index("IX_CatGalleryImages_StorageKey", IsUnique = true)]
        public string StorageKey { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;
        
        public long FileSize { get; set; }
        
        [MaxLength(100)]
        public string ContentType { get; set; } = "image/jpeg";
        
        [MaxLength(100)]
        public string MimeType { get; set; } = "image/jpeg";
        
        [MaxLength(20)]
        public string Format { get; set; } = "jpg";
        
        // Image properties for responsive design
        public int Width { get; set; }
        public int Height { get; set; }
        
        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public float? AspectRatio { get; set; } // Computed: Width/Height
        
        // Cat metadata - PERFORMANCE CRITICAL (heavily indexed)
        [MaxLength(100)]
        [Index("IX_CatGalleryImages_CatName")]
        public string? CatName { get; set; }
        
        [MaxLength(50)]
        [Index("IX_CatGalleryImages_CatId")]
        public string? CatId { get; set; }
        
        [Required]
        [MaxLength(50)]
        [Index("IX_CatGalleryImages_Category_DateTaken", Order = 0)]
        public string Category { get; set; } = string.Empty; // studs, queens, kittens, gallery
        
        // Descriptive metadata
        [MaxLength(200)]
        public string? Title { get; set; }
        
        [MaxLength(1000)]
        public string? Description { get; set; }
        
        [MaxLength(500)]
        public string? Alt { get; set; } // Accessibility description
        
        [MaxLength(500)]
        public string? Tags { get; set; } // Comma-separated for simple searching
        
        [MaxLength(2000)]
        public string? ExifData { get; set; } // JSON string of EXIF metadata
        
        // Cat-specific details
        [MaxLength(50)]
        public string? AgeAtPhoto { get; set; } // "6 months", "2 years", etc.
        
        [MaxLength(100)]
        public string Breed { get; set; } = "Maine Coon";
        
        [MaxLength(100)]
        public string? Bloodline { get; set; }
        
        [MaxLength(1)]
        public string? Gender { get; set; } // M/F
        
        // Timestamps - CRITICAL for sorting performance
        [Index("IX_CatGalleryImages_Category_DateTaken", Order = 1)]
        public DateTime? DateTaken { get; set; }
        
        public DateTime DateUploaded { get; set; } = DateTime.UtcNow;
        
        public DateTime DateModified { get; set; } = DateTime.UtcNow;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? LastViewedAt { get; set; }
        
        // Status and visibility
        [Index("IX_CatGalleryImages_IsActive_IsPublic")]
        public bool IsActive { get; set; } = true;
        
        [Index("IX_CatGalleryImages_IsActive_IsPublic")]
        public bool IsPublic { get; set; } = true;
        
        public bool IsFeatured { get; set; } = false;
        
        public int DisplayOrder { get; set; } = 0;
        
        public int SortOrder { get; set; } = 0;
        
        // Analytics properties
        public int ViewCount { get; set; } = 0;
        public int LikeCount { get; set; } = 0;
        public int DownloadCount { get; set; } = 0;
        
        // Dual Storage Provider Support (S3 + B2)
        [Required]
        [MaxLength(10)]
        public string StorageProvider { get; set; } = "S3"; // S3 or B2
        
        [MaxLength(100)]
        public string? StorageFileId { get; set; } // S3 ETag or B2 file ID
        
        [Required]
        [MaxLength(100)]
        public string StorageBucketName { get; set; } = string.Empty;
        
        // S3 specific fields (for backward compatibility)
        [MaxLength(500)]
        public string? S3Key { get; set; }
        
        [MaxLength(100)]
        public string? S3Bucket { get; set; }
        
        [MaxLength(1000)]
        public string? S3Url { get; set; }
        
        // B2 specific fields
        [MaxLength(500)]
        public string? B2Key { get; set; }
        
        [MaxLength(100)]
        public string? B2Bucket { get; set; }
        
        [MaxLength(1000)]
        public string? B2Url { get; set; }
        
        [MaxLength(100)]
        public string? B2FileId { get; set; }
        
        // Computed property for public URL based on storage provider
        [NotMapped]
        public string PublicUrl => StorageProvider switch
        {
            "B2" => !string.IsNullOrEmpty(B2Url) ? B2Url : 
                    !string.IsNullOrEmpty(B2Key) && !string.IsNullOrEmpty(B2Bucket)
                        ? $"https://f002.backblazeb2.com/file/{B2Bucket}/{B2Key}"
                        : string.Empty,
            "S3" => !string.IsNullOrEmpty(S3Url) ? S3Url :
                    !string.IsNullOrEmpty(S3Key) && !string.IsNullOrEmpty(S3Bucket)
                        ? $"https://{S3Bucket}.s3.amazonaws.com/{S3Key}"
                        : string.Empty,
            _ => string.Empty
        };
        
        // Performance optimization fields
        [MaxLength(500)]
        public string? ThumbnailStorageKey { get; set; } // Pre-generated thumbnail
        
        public DateTime? LastAccessedAt { get; set; }
        public int AccessCount { get; set; } = 0;
        
        // Audit fields
        [MaxLength(100)]
        public string? CreatedBy { get; set; }
        
        [MaxLength(100)]
        public string? ModifiedBy { get; set; }
        
        // Navigation properties
        [ForeignKey(nameof(CatId))]
        public virtual CatProfile? CatProfile { get; set; }
        
        // Methods for backward compatibility with existing CatImageMetadata
        public static CatGalleryImage FromMetadata(CatImageMetadata metadata, string storageKey, string bucketName, string storageProvider = "S3")
        {
            var image = new CatGalleryImage
            {
                StorageKey = storageKey,
                StorageBucketName = bucketName,
                StorageProvider = storageProvider,
                OriginalFileName = Path.GetFileName(storageKey),
                FileSize = metadata.FileSize,
                ContentType = metadata.ContentType,
                Width = metadata.Width,
                Height = metadata.Height,
                CatName = metadata.Name,
                CatId = metadata.CatId?.ToString(),
                Category = metadata.Category ?? "gallery",
                Title = metadata.RegisteredName,
                Description = metadata.Description,
                Tags = metadata.Tags,
                AgeAtPhoto = metadata.AgeAtPhoto,
                Breed = metadata.Breed ?? "Maine Coon",
                Bloodline = metadata.Bloodline,
                Gender = metadata.Gender,
                DateTaken = metadata.DateTaken,
                DateUploaded = metadata.DateUploaded ?? DateTime.UtcNow,
                DateModified = DateTime.UtcNow,
                CreatedBy = "MIGRATION"
            };
            
            // Set storage-specific fields
            if (storageProvider == "S3")
            {
                image.S3Key = storageKey;
                image.S3Bucket = bucketName;
            }
            else if (storageProvider == "B2")
            {
                image.B2Key = storageKey;
                image.B2Bucket = bucketName;
            }
            
            return image;
        }
        
        public CatImageMetadata ToMetadata()
        {
            return new CatImageMetadata
            {
                Name = CatName ?? "",
                Gender = Gender ?? "",
                DateUploaded = DateUploaded,
                FileFormat = Path.GetExtension(OriginalFileName),
                ContentType = ContentType,
                FileSize = FileSize,
                Width = Width,
                Height = Height,
                Description = Description,
                AgeAtPhoto = AgeAtPhoto,
                Bloodline = Bloodline,
                Breed = Breed,
                DateTaken = DateTaken,
                Category = Category,
                Tags = Tags,
                CatId = !string.IsNullOrEmpty(CatId) && int.TryParse(CatId, out var id) ? id : null,
                RegisteredName = Title
            };
        }
        
        // Helper methods for storage provider switching
        public void MigrateToB2(string b2Key, string b2BucketName, string? b2FileId = null)
        {
            // Preserve S3 data for rollback capability
            B2Key = b2Key;
            B2Bucket = b2BucketName;
            B2FileId = b2FileId;
            
            // Update primary storage fields
            StorageKey = b2Key;
            StorageBucketName = b2BucketName;
            StorageProvider = "B2";
            StorageFileId = b2FileId;
            
            DateModified = DateTime.UtcNow;
        }
        
        public void RollbackToS3()
        {
            if (!string.IsNullOrEmpty(S3Key) && !string.IsNullOrEmpty(S3Bucket))
            {
                StorageKey = S3Key;
                StorageBucketName = S3Bucket;
                StorageProvider = "S3";
                StorageFileId = null; // S3 uses ETag differently
                
                DateModified = DateTime.UtcNow;
            }
        }
        
        // Performance tracking methods
        public void IncrementAccessCount()
        {
            AccessCount++;
            LastAccessedAt = DateTime.UtcNow;
        }
        
        public void UpdateLastAccessed()
        {
            LastAccessedAt = DateTime.UtcNow;
        }
    }
}
