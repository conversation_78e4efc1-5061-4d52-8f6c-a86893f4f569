using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Cat profile entity for comprehensive cat management and pedigree tracking
    /// Supports the full breeding program with lineage, registration, and show data
    /// </summary>
    [Table("CatProfiles")]
    public class CatProfile
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(50)]
        [Index("IX_CatProfiles_CatId", IsUnique = true)]
        public string CatId { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        [Index("IX_CatProfiles_CatName")]
        public string CatName { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Breed { get; set; } = "Maine Coon";
        
        [MaxLength(100)]
        public string? Bloodline { get; set; }
        
        [MaxLength(1)]
        public string? Gender { get; set; } // M/F
        
        public DateTime? BirthDate { get; set; }
        
        // Computed property for current age
        [NotMapped]
        public string CurrentAge
        {
            get
            {
                if (!BirthDate.HasValue)
                    return "Unknown";
                
                var age = DateTime.Now - BirthDate.Value;
                var years = (int)(age.TotalDays / 365.25);
                var months = (int)((age.TotalDays % 365.25) / 30.44);
                
                if (years == 0)
                    return months == 1 ? "1 month" : $"{months} months";
                else if (months == 0)
                    return years == 1 ? "1 year" : $"{years} years";
                else
                    return $"{years} years, {months} months";
            }
        }
        
        [MaxLength(50)]
        [Index("IX_CatProfiles_BreedingStatus")]
        public string? BreedingStatus { get; set; } // Available, Breeding, Retired, Kitten
        
        [MaxLength(50)]
        [Index("IX_CatProfiles_AvailabilityStatus")]
        public string? AvailabilityStatus { get; set; } // Available, Reserved, Sold, Not-For-Sale
        
        // Pedigree relationships
        [MaxLength(50)]
        public string? FatherId { get; set; }
        
        [MaxLength(50)]
        public string? MotherId { get; set; }
        
        // Show and registration information
        [MaxLength(500)]
        public string? ChampionTitles { get; set; }
        
        [MaxLength(100)]
        public string? RegistrationNumber { get; set; }
        
        [MaxLength(200)]
        public string? RegisteredName { get; set; }
        
        [MaxLength(100)]
        public string? RegistrationBody { get; set; } // TICA, CFA, etc.
        
        // Physical characteristics
        [MaxLength(100)]
        public string? Color { get; set; }
        
        [MaxLength(100)]
        public string? Pattern { get; set; }
        
        [MaxLength(200)]
        public string? Markings { get; set; }
        
        [MaxLength(50)]
        public string? EyeColor { get; set; }
        
        public decimal? Weight { get; set; } // in kg
        
        public decimal? Length { get; set; } // in cm
        
        public decimal? Height { get; set; } // in cm
        
        // Health and genetics
        [MaxLength(1000)]
        public string? HealthRecords { get; set; }
        
        [MaxLength(500)]
        public string? GeneticTesting { get; set; }
        
        [MaxLength(500)]
        public string? Vaccinations { get; set; }
        
        public DateTime? LastHealthCheck { get; set; }
        
        [MaxLength(200)]
        public string? VeterinarianContact { get; set; }
        
        // Personality and behavior
        [MaxLength(1000)]
        public string? PersonalityDescription { get; set; }
        
        [MaxLength(500)]
        public string? BehaviorTraits { get; set; }
        
        [MaxLength(500)]
        public string? SpecialNeeds { get; set; }
        
        // Breeding information
        public int? TotalLitters { get; set; }
        
        public DateTime? FirstBreedingDate { get; set; }
        
        public DateTime? LastBreedingDate { get; set; }
        
        [MaxLength(1000)]
        public string? BreedingNotes { get; set; }
        
        // Location and care
        [MaxLength(200)]
        public string? CurrentLocation { get; set; }
        
        [MaxLength(200)]
        public string? CaregiverName { get; set; }
        
        [MaxLength(200)]
        public string? CaregiverContact { get; set; }
        
        // Profile image
        [MaxLength(500)]
        public string? ProfileImageStorageKey { get; set; }
        
        [MaxLength(10)]
        public string? ProfileImageStorageProvider { get; set; } // S3 or B2
        
        // Computed property for profile image URL
        [NotMapped]
        public string? ProfileImageUrl
        {
            get
            {
                if (string.IsNullOrEmpty(ProfileImageStorageKey))
                    return null;
                
                return ProfileImageStorageProvider switch
                {
                    "B2" => $"https://f002.backblazeb2.com/file/yendor-profiles/{ProfileImageStorageKey}",
                    "S3" => $"https://yendor-profiles.s3.amazonaws.com/{ProfileImageStorageKey}",
                    _ => null
                };
            }
        }
        
        // Financial information
        public decimal? PurchasePrice { get; set; }
        
        public decimal? CurrentValue { get; set; }
        
        public decimal? StudFee { get; set; }
        
        public decimal? KittenPrice { get; set; }
        
        [MaxLength(500)]
        public string? PricingNotes { get; set; }
        
        // Status and visibility
        public bool IsActive { get; set; } = true;
        
        public bool IsPublic { get; set; } = true;
        
        public bool IsFeatured { get; set; } = false;
        
        public int DisplayOrder { get; set; } = 0;
        
        // Timestamps
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime ModifiedAt { get; set; } = DateTime.UtcNow;
        
        // Audit fields
        [MaxLength(100)]
        public string? CreatedBy { get; set; }
        
        [MaxLength(100)]
        public string? ModifiedBy { get; set; }
        
        // Additional metadata
        [MaxLength(1000)]
        public string? Notes { get; set; }
        
        [MaxLength(500)]
        public string? Tags { get; set; } // Comma-separated tags
        
        // Navigation properties
        public virtual ICollection<CatGalleryImage> GalleryImages { get; set; } = new List<CatGalleryImage>();
        
        [ForeignKey(nameof(FatherId))]
        public virtual CatProfile? Father { get; set; }
        
        [ForeignKey(nameof(MotherId))]
        public virtual CatProfile? Mother { get; set; }
        
        public virtual ICollection<CatProfile> Offspring { get; set; } = new List<CatProfile>();
        
        // Helper methods
        public void UpdateModifiedTimestamp(string? modifiedBy = null)
        {
            ModifiedAt = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(modifiedBy))
                ModifiedBy = modifiedBy;
        }
        
        public bool IsKitten()
        {
            return BirthDate.HasValue && (DateTime.Now - BirthDate.Value).TotalDays < 365;
        }
        
        public bool IsBreedingAge()
        {
            if (!BirthDate.HasValue) return false;
            
            var ageInMonths = (DateTime.Now - BirthDate.Value).TotalDays / 30.44;
            return Gender switch
            {
                "M" => ageInMonths >= 12, // Males can breed at 12 months
                "F" => ageInMonths >= 8,  // Females can breed at 8 months
                _ => false
            };
        }
        
        public string GetBreedingStatusSummary()
        {
            if (!IsBreedingAge())
                return "Too young for breeding";
            
            return BreedingStatus switch
            {
                "Available" => $"Available for breeding - {Gender switch { "M" => "Stud", "F" => "Queen", _ => "Cat" }}",
                "Breeding" => "Currently breeding",
                "Retired" => "Retired from breeding",
                "Kitten" => "Kitten - not yet breeding age",
                _ => "Status unknown"
            };
        }
        
        public List<string> GetTagsList()
        {
            if (string.IsNullOrEmpty(Tags))
                return new List<string>();
            
            return Tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                      .Select(tag => tag.Trim())
                      .Where(tag => !string.IsNullOrEmpty(tag))
                      .ToList();
        }
        
        public void SetTags(IEnumerable<string> tags)
        {
            Tags = string.Join(", ", tags.Where(tag => !string.IsNullOrWhiteSpace(tag)));
        }
        
        // Pedigree helper methods
        public string GetPedigreeDisplay(int generations = 3)
        {
            var pedigree = new List<string>();
            
            if (!string.IsNullOrEmpty(RegisteredName))
                pedigree.Add($"Self: {RegisteredName}");
            
            if (Father != null && !string.IsNullOrEmpty(Father.RegisteredName))
                pedigree.Add($"Sire: {Father.RegisteredName}");
            
            if (Mother != null && !string.IsNullOrEmpty(Mother.RegisteredName))
                pedigree.Add($"Dam: {Mother.RegisteredName}");
            
            return string.Join(" | ", pedigree);
        }
        
        public decimal? GetEstimatedValue()
        {
            if (CurrentValue.HasValue)
                return CurrentValue;
            
            // Simple estimation based on breeding status and titles
            decimal baseValue = 1000; // Base value for Maine Coon
            
            if (!string.IsNullOrEmpty(ChampionTitles))
                baseValue *= 2; // Champions are worth more
            
            if (BreedingStatus == "Available" && IsBreedingAge())
                baseValue *= 1.5m; // Breeding cats are worth more
            
            return baseValue;
        }
    }
}