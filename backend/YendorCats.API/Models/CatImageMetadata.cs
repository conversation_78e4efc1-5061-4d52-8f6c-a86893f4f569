using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Represents metadata for a cat image stored in S3
    /// </summary>
    public class CatImageMetadata
    {
        #region File Metadata (System Generated)
        
        /// <summary>
        /// The date the image was uploaded (set automatically)
        /// </summary>
        [Required]
        public DateTime DateUploaded { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// The file format/extension
        /// </summary>
        [Required]
        public string FileFormat { get; set; } = string.Empty;
        
        /// <summary>
        /// The content type (MIME type) of the image
        /// </summary>
        [Required]
        public string ContentType { get; set; } = string.Empty;
        
        /// <summary>
        /// The size of the file in bytes
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// The width of the image in pixels
        /// </summary>
        public int? Width { get; set; }
        
        /// <summary>
        /// The height of the image in pixels
        /// </summary>
        public int? Height { get; set; }
        
        /// <summary>
        /// The IP address of the uploader
        /// </summary>
        public string? UploaderIp { get; set; }
        
        /// <summary>
        /// The user agent of the uploader
        /// </summary>
        public string? UploaderUserAgent { get; set; }
        
        /// <summary>
        /// A unique identifier for the upload session
        /// </summary>
        public string? UploadSessionId { get; set; }
        
        #endregion
        
        #region User Inputted Data
        
        /// <summary>
        /// The name of the cat in the picture
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Description of the cat or image
        /// </summary>
        public string? Description { get; set; }
        
        /// <summary>
        /// The age of the cat (in years.months format, e.g., "2.5")
        /// </summary>
        public string? Age { get; set; }
        
        /// <summary>
        /// The gender of the cat (M/F)
        /// </summary>
        [Required]
        public string Gender { get; set; } = string.Empty;
        
        /// <summary>
        /// The bloodline of the cat
        /// </summary>
        public string? Bloodline { get; set; }
        
        /// <summary>
        /// The breed of the cat
        /// </summary>
        public string? Breed { get; set; }
        
        /// <summary>
        /// The hair/fur color of the cat
        /// </summary>
        public string? HairColor { get; set; }
        
        /// <summary>
        /// The date the photo was taken
        /// </summary>
        public DateTime? DateTaken { get; set; }
        
        /// <summary>
        /// Description of the cat's personality
        /// </summary>
        public string? Personality { get; set; }
        
        /// <summary>
        /// The category the cat belongs to (e.g., "studs", "queens", "kittens")
        /// </summary>
        public string? Category { get; set; }
        
        /// <summary>
        /// Tags for filtering and searching
        /// </summary>
        public string? Tags { get; set; }
        
        /// <summary>
        /// The parent/mother of the cat
        /// </summary>
        public string? Mother { get; set; }
        
        /// <summary>
        /// The parent/father of the cat
        /// </summary>
        public string? Father { get; set; }
        
        #endregion
        
        #region Database Relationship Fields
        
        /// <summary>
        /// Database Cat ID for bidirectional linking
        /// </summary>
        public int? CatId { get; set; }
        
        /// <summary>
        /// Database ID of the mother cat
        /// </summary>
        public int? MotherCatId { get; set; }
        
        /// <summary>
        /// Database ID of the father cat
        /// </summary>
        public int? FatherCatId { get; set; }
        
        /// <summary>
        /// Official registered name from pedigree
        /// </summary>
        public string? RegisteredName { get; set; }
        
        /// <summary>
        /// Registration number from pedigree papers
        /// </summary>
        public string? RegistrationNumber { get; set; }
        
        /// <summary>
        /// Breeding status (available-kitten, breeding-queen, stud, retired)
        /// </summary>
        public string? BreedingStatus { get; set; }
        
        /// <summary>
        /// Availability status (available, reserved, sold, not-for-sale)
        /// </summary>
        public string? AvailabilityStatus { get; set; }
        
        /// <summary>
        /// Type of photo (profile, action, family, breeding, growth)
        /// </summary>
        public string? PhotoType { get; set; }
        
        /// <summary>
        /// Age when photo was taken
        /// </summary>
        public string? AgeAtPhoto { get; set; }
        
        /// <summary>
        /// Champion titles and achievements
        /// </summary>
        public string? ChampionTitles { get; set; }
        
        /// <summary>
        /// Generation level in pedigree (1=parent, 2=grandparent, etc.)
        /// </summary>
        public string? GenerationLevel { get; set; }
        
        #endregion
        
        /// <summary>
        /// Additional metadata as key-value pairs
        /// </summary>
        public Dictionary<string, string> AdditionalMetadata { get; set; } = new();
        
        /// <summary>
        /// Convert the metadata to a dictionary for S3 storage
        /// </summary>
        /// <returns>Dictionary containing all metadata fields</returns>
        public Dictionary<string, string> ToS3Metadata()
        {
            var metadata = new Dictionary<string, string>
            {
                // Required fields
                ["name"] = Name,
                ["gender"] = Gender,
                ["date_uploaded"] = DateUploaded.ToString("o"),
                ["file_format"] = FileFormat,
                ["content_type"] = ContentType
            };
            
            // Optional fields - only add if they have values
            if (!string.IsNullOrEmpty(Description)) metadata["description"] = Description;
            if (!string.IsNullOrEmpty(Age)) metadata["age"] = Age;
            if (!string.IsNullOrEmpty(Bloodline)) metadata["bloodline"] = Bloodline;
            if (!string.IsNullOrEmpty(Breed)) metadata["breed"] = Breed;
            if (!string.IsNullOrEmpty(HairColor)) metadata["hair_color"] = HairColor;
            if (DateTaken.HasValue) metadata["date_taken"] = DateTaken.Value.ToString("o");
            if (!string.IsNullOrEmpty(Personality)) metadata["personality"] = Personality;
            if (!string.IsNullOrEmpty(Category)) metadata["category"] = Category;
            if (!string.IsNullOrEmpty(Tags)) metadata["tags"] = Tags;
            if (!string.IsNullOrEmpty(Mother)) metadata["mother"] = Mother;
            if (!string.IsNullOrEmpty(Father)) metadata["father"] = Father;
            
            // Database relationship fields
            if (CatId.HasValue) metadata["cat_id"] = CatId.Value.ToString();
            if (MotherCatId.HasValue) metadata["mother_cat_id"] = MotherCatId.Value.ToString();
            if (FatherCatId.HasValue) metadata["father_cat_id"] = FatherCatId.Value.ToString();
            if (!string.IsNullOrEmpty(RegisteredName)) metadata["registered_name"] = RegisteredName;
            if (!string.IsNullOrEmpty(RegistrationNumber)) metadata["registration_number"] = RegistrationNumber;
            if (!string.IsNullOrEmpty(BreedingStatus)) metadata["breeding_status"] = BreedingStatus;
            if (!string.IsNullOrEmpty(AvailabilityStatus)) metadata["availability_status"] = AvailabilityStatus;
            if (!string.IsNullOrEmpty(PhotoType)) metadata["photo_type"] = PhotoType;
            if (!string.IsNullOrEmpty(AgeAtPhoto)) metadata["age_at_photo"] = AgeAtPhoto;
            if (!string.IsNullOrEmpty(ChampionTitles)) metadata["champion_titles"] = ChampionTitles;
            if (!string.IsNullOrEmpty(GenerationLevel)) metadata["generation_level"] = GenerationLevel;
            
            // System metadata
            if (FileSize > 0) metadata["file_size"] = FileSize.ToString();
            if (Width.HasValue) metadata["width"] = Width.Value.ToString();
            if (Height.HasValue) metadata["height"] = Height.Value.ToString();
            if (!string.IsNullOrEmpty(UploaderIp)) metadata["uploader_ip"] = UploaderIp;
            if (!string.IsNullOrEmpty(UploaderUserAgent)) metadata["uploader_user_agent"] = UploaderUserAgent;
            if (!string.IsNullOrEmpty(UploadSessionId)) metadata["upload_session_id"] = UploadSessionId;
            
            // Add any additional metadata
            foreach (var kvp in AdditionalMetadata)
            {
                if (!metadata.ContainsKey(kvp.Key))
                {
                    metadata[kvp.Key] = kvp.Value;
                }
            }
            
            return metadata;
        }
        
        /// <summary>
        /// Create a CatImageMetadata object from S3 metadata
        /// </summary>
        /// <param name="metadata">The S3 object metadata</param>
        /// <returns>A CatImageMetadata object</returns>
        public static CatImageMetadata FromS3Metadata(Dictionary<string, string> metadata)
        {
            var result = new CatImageMetadata();
            
            // Extract metadata fields
            if (metadata.TryGetValue("name", out var name)) result.Name = name;
            if (metadata.TryGetValue("description", out var description)) result.Description = description;
            if (metadata.TryGetValue("age", out var age)) result.Age = age;
            if (metadata.TryGetValue("gender", out var gender)) result.Gender = gender;
            if (metadata.TryGetValue("bloodline", out var bloodline)) result.Bloodline = bloodline;
            if (metadata.TryGetValue("breed", out var breed)) result.Breed = breed;
            if (metadata.TryGetValue("hair_color", out var hairColor)) result.HairColor = hairColor;
            if (metadata.TryGetValue("personality", out var personality)) result.Personality = personality;
            if (metadata.TryGetValue("category", out var category)) result.Category = category;
            if (metadata.TryGetValue("tags", out var tags)) result.Tags = tags;
            if (metadata.TryGetValue("mother", out var mother)) result.Mother = mother;
            if (metadata.TryGetValue("father", out var father)) result.Father = father;
            
            // Parse database relationship fields
            if (metadata.TryGetValue("cat_id", out var catIdStr) &&
                int.TryParse(catIdStr, out var catId))
            {
                result.CatId = catId;
            }
            
            if (metadata.TryGetValue("mother_cat_id", out var motherCatIdStr) &&
                int.TryParse(motherCatIdStr, out var motherCatId))
            {
                result.MotherCatId = motherCatId;
            }
            
            if (metadata.TryGetValue("father_cat_id", out var fatherCatIdStr) &&
                int.TryParse(fatherCatIdStr, out var fatherCatId))
            {
                result.FatherCatId = fatherCatId;
            }
            
            if (metadata.TryGetValue("registered_name", out var registeredName)) result.RegisteredName = registeredName;
            if (metadata.TryGetValue("registration_number", out var registrationNumber)) result.RegistrationNumber = registrationNumber;
            if (metadata.TryGetValue("breeding_status", out var breedingStatus)) result.BreedingStatus = breedingStatus;
            if (metadata.TryGetValue("availability_status", out var availabilityStatus)) result.AvailabilityStatus = availabilityStatus;
            if (metadata.TryGetValue("photo_type", out var photoType)) result.PhotoType = photoType;
            if (metadata.TryGetValue("age_at_photo", out var ageAtPhoto)) result.AgeAtPhoto = ageAtPhoto;
            if (metadata.TryGetValue("champion_titles", out var championTitles)) result.ChampionTitles = championTitles;
            if (metadata.TryGetValue("generation_level", out var generationLevel)) result.GenerationLevel = generationLevel;
            
            // Parse date fields
            if (metadata.TryGetValue("date_uploaded", out var dateUploadedStr) && 
                DateTime.TryParse(dateUploadedStr, out var dateUploaded))
            {
                result.DateUploaded = dateUploaded;
            }
            
            if (metadata.TryGetValue("date_taken", out var dateTakenStr) && 
                DateTime.TryParse(dateTakenStr, out var dateTaken))
            {
                result.DateTaken = dateTaken;
            }
            
            // Parse numeric fields
            if (metadata.TryGetValue("file_size", out var fileSizeStr) && 
                long.TryParse(fileSizeStr, out var fileSize))
            {
                result.FileSize = fileSize;
            }
            
            if (metadata.TryGetValue("width", out var widthStr) && 
                int.TryParse(widthStr, out var width))
            {
                result.Width = width;
            }
            
            if (metadata.TryGetValue("height", out var heightStr) && 
                int.TryParse(heightStr, out var height))
            {
                result.Height = height;
            }
            
            // System fields
            if (metadata.TryGetValue("file_format", out var fileFormat)) result.FileFormat = fileFormat;
            if (metadata.TryGetValue("content_type", out var contentType)) result.ContentType = contentType;
            if (metadata.TryGetValue("uploader_ip", out var uploaderIp)) result.UploaderIp = uploaderIp;
            if (metadata.TryGetValue("uploader_user_agent", out var userAgent)) result.UploaderUserAgent = userAgent;
            if (metadata.TryGetValue("upload_session_id", out var sessionId)) result.UploadSessionId = sessionId;
            
            // Add any remaining metadata to the additional metadata dictionary
            foreach (var kvp in metadata)
            {
                string key = kvp.Key.ToLowerInvariant();
                if (key != "name" && key != "description" && key != "age" &&
                    key != "gender" && key != "bloodline" && key != "breed" &&
                    key != "hair_color" && key != "date_taken" && key != "personality" &&
                    key != "category" && key != "tags" && key != "mother" &&
                    key != "father" && key != "date_uploaded" && key != "file_format" &&
                    key != "content_type" && key != "file_size" && key != "width" &&
                    key != "height" && key != "uploader_ip" && key != "uploader_user_agent" &&
                    key != "upload_session_id" && key != "cat_id" && key != "mother_cat_id" &&
                    key != "father_cat_id" && key != "registered_name" && key != "registration_number" &&
                    key != "breeding_status" && key != "availability_status" && key != "photo_type" &&
                    key != "age_at_photo" && key != "champion_titles" && key != "generation_level")
                {
                    result.AdditionalMetadata[kvp.Key] = kvp.Value;
                }
            }
            
            return result;
        }
    }
}
