using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace YendorCats.API.Models
{
    /// <summary>
    /// B2 synchronization log for tracking all storage operations and migrations
    /// Critical for audit trails and data integrity verification
    /// </summary>
    [Table("B2SyncLogs")]
    public class B2SyncLog
    {
        [Key]
        public long Id { get; set; }
        
        // Operation identification
        [Required]
        [MaxLength(500)]
        [Index("IX_B2SyncLogs_StorageKey")]
        public string StorageKey { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(50)]
        [Index("IX_B2SyncLogs_Operation")]
        public string Operation { get; set; } = string.Empty; // INSERT, UPDATE, DELETE, VERIFY, MIGRATE
        
        [Required]
        [MaxLength(50)]
        [Index("IX_B2SyncLogs_Status_SyncedAt", Order = 0)]
        public string Status { get; set; } = string.Empty; // SUCCESS, FAILED, PENDING, RETRY
        
        // Source and destination information
        [MaxLength(10)]
        public string? SourceProvider { get; set; } // S3, B2, DATABASE
        
        [MaxLength(10)]
        public string? DestinationProvider { get; set; } // S3, B2, DATABASE
        
        [MaxLength(100)]
        public string? SourceBucket { get; set; }
        
        [MaxLength(100)]
        public string? DestinationBucket { get; set; }
        
        [MaxLength(500)]
        public string? SourceKey { get; set; }
        
        [MaxLength(500)]
        public string? DestinationKey { get; set; }
        
        // File information
        [MaxLength(255)]
        public string? OriginalFileName { get; set; }
        
        public long? FileSize { get; set; }
        
        [MaxLength(100)]
        public string? ContentType { get; set; }
        
        [MaxLength(100)]
        public string? FileHash { get; set; } // MD5 or SHA256 hash for integrity
        
        // Metadata information
        [MaxLength(1000)]
        public string? MetadataJson { get; set; } // JSON serialized metadata
        
        public int? MetadataFieldCount { get; set; }
        
        // Timing and performance
        [Index("IX_B2SyncLogs_Status_SyncedAt", Order = 1)]
        public DateTime SyncedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? StartedAt { get; set; }
        
        public DateTime? CompletedAt { get; set; }
        
        public int? DurationMs { get; set; }
        
        // Retry and error handling
        public int RetryCount { get; set; } = 0;
        
        public int MaxRetries { get; set; } = 3;
        
        [MaxLength(2000)]
        public string? ErrorMessage { get; set; }
        
        [MaxLength(500)]
        public string? ErrorCode { get; set; }
        
        public DateTime? NextRetryAt { get; set; }
        
        // User and session tracking
        [MaxLength(100)]
        public string? UserId { get; set; }
        
        [MaxLength(100)]
        public string? SessionId { get; set; }
        
        [MaxLength(200)]
        public string? UserAgent { get; set; }
        
        [MaxLength(50)]
        public string? IpAddress { get; set; }
        
        // Batch and migration tracking
        [MaxLength(100)]
        public string? BatchId { get; set; }
        
        [MaxLength(100)]
        public string? MigrationId { get; set; }
        
        public int? BatchSequence { get; set; }
        
        public int? TotalBatchItems { get; set; }
        
        // Related entity information
        public long? CatGalleryImageId { get; set; }
        
        public int? CatProfileId { get; set; }
        
        [MaxLength(50)]
        public string? CatId { get; set; }
        
        [MaxLength(50)]
        public string? Category { get; set; }
        
        // Verification and validation
        public bool IsVerified { get; set; } = false;
        
        public DateTime? VerifiedAt { get; set; }
        
        [MaxLength(100)]
        public string? VerifiedBy { get; set; }
        
        public bool RequiresManualReview { get; set; } = false;
        
        [MaxLength(1000)]
        public string? ReviewNotes { get; set; }
        
        // Additional context
        [MaxLength(500)]
        public string? Context { get; set; } // Additional context about the operation
        
        [MaxLength(1000)]
        public string? Notes { get; set; }
        
        // Computed properties
        [NotMapped]
        public bool IsCompleted => Status == "SUCCESS" || Status == "FAILED";
        
        [NotMapped]
        public bool NeedsRetry => Status == "FAILED" && RetryCount < MaxRetries && 
                                 (NextRetryAt == null || NextRetryAt <= DateTime.UtcNow);
        
        [NotMapped]
        public TimeSpan? Duration => StartedAt.HasValue && CompletedAt.HasValue 
            ? CompletedAt - StartedAt : null;
        
        [NotMapped]
        public string OperationSummary => $"{Operation} {StorageKey} from {SourceProvider} to {DestinationProvider}";
        
        // Navigation properties
        [ForeignKey(nameof(CatGalleryImageId))]
        public virtual CatGalleryImage? CatGalleryImage { get; set; }
        
        [ForeignKey(nameof(CatProfileId))]
        public virtual CatProfile? CatProfile { get; set; }
        
        // Factory methods for common operations
        public static B2SyncLog CreateMigrationLog(string storageKey, string sourceProvider, 
            string destinationProvider, string migrationId, string? batchId = null)
        {
            return new B2SyncLog
            {
                StorageKey = storageKey,
                Operation = "MIGRATE",
                Status = "PENDING",
                SourceProvider = sourceProvider,
                DestinationProvider = destinationProvider,
                MigrationId = migrationId,
                BatchId = batchId,
                StartedAt = DateTime.UtcNow
            };
        }
        
        public static B2SyncLog CreateUploadLog(string storageKey, string destinationProvider, 
            string fileName, long fileSize, string contentType, string? userId = null)
        {
            return new B2SyncLog
            {
                StorageKey = storageKey,
                Operation = "INSERT",
                Status = "PENDING",
                DestinationProvider = destinationProvider,
                OriginalFileName = fileName,
                FileSize = fileSize,
                ContentType = contentType,
                UserId = userId,
                StartedAt = DateTime.UtcNow
            };
        }
        
        public static B2SyncLog CreateDeleteLog(string storageKey, string sourceProvider, 
            string? userId = null)
        {
            return new B2SyncLog
            {
                StorageKey = storageKey,
                Operation = "DELETE",
                Status = "PENDING",
                SourceProvider = sourceProvider,
                UserId = userId,
                StartedAt = DateTime.UtcNow
            };
        }
        
        public static B2SyncLog CreateVerificationLog(string storageKey, string provider)
        {
            return new B2SyncLog
            {
                StorageKey = storageKey,
                Operation = "VERIFY",
                Status = "PENDING",
                SourceProvider = provider,
                StartedAt = DateTime.UtcNow
            };
        }
        
        // Instance methods
        public void MarkAsStarted()
        {
            StartedAt = DateTime.UtcNow;
            Status = "PENDING";
        }
        
        public void MarkAsCompleted(bool success = true, string? errorMessage = null)
        {
            CompletedAt = DateTime.UtcNow;
            Status = success ? "SUCCESS" : "FAILED";
            
            if (StartedAt.HasValue)
            {
                DurationMs = (int)(CompletedAt.Value - StartedAt.Value).TotalMilliseconds;
            }
            
            if (!success && !string.IsNullOrEmpty(errorMessage))
            {
                ErrorMessage = errorMessage;
            }
        }
        
        public void MarkAsRetry(string? errorMessage = null, int? retryDelayMinutes = null)
        {
            RetryCount++;
            Status = "RETRY";
            
            if (!string.IsNullOrEmpty(errorMessage))
            {
                ErrorMessage = errorMessage;
            }
            
            // Calculate next retry time with exponential backoff
            var baseDelay = retryDelayMinutes ?? (int)Math.Pow(2, RetryCount); // 2, 4, 8 minutes
            NextRetryAt = DateTime.UtcNow.AddMinutes(baseDelay);
        }
        
        public void MarkAsVerified(string? verifiedBy = null)
        {
            IsVerified = true;
            VerifiedAt = DateTime.UtcNow;
            VerifiedBy = verifiedBy;
        }
        
        public void AddContext(string contextInfo)
        {
            if (string.IsNullOrEmpty(Context))
                Context = contextInfo;
            else
                Context += $" | {contextInfo}";
        }
        
        public void AddNote(string note)
        {
            if (string.IsNullOrEmpty(Notes))
                Notes = note;
            else
                Notes += $"\n{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}: {note}";
        }
        
        public void SetFileHash(string hash)
        {
            FileHash = hash;
        }
        
        public void SetMetadata(string metadataJson, int fieldCount)
        {
            MetadataJson = metadataJson;
            MetadataFieldCount = fieldCount;
        }
        
        public void SetBatchInfo(string batchId, int sequence, int totalItems)
        {
            BatchId = batchId;
            BatchSequence = sequence;
            TotalBatchItems = totalItems;
        }
        
        public void SetUserInfo(string? userId, string? sessionId, string? userAgent, string? ipAddress)
        {
            UserId = userId;
            SessionId = sessionId;
            UserAgent = userAgent;
            IpAddress = ipAddress;
        }
        
        public void LinkToGalleryImage(long galleryImageId)
        {
            CatGalleryImageId = galleryImageId;
        }
        
        public void LinkToCatProfile(int catProfileId)
        {
            CatProfileId = catProfileId;
        }
        
        // Query helper methods
        public bool IsOlderThan(TimeSpan timeSpan)
        {
            return SyncedAt < DateTime.UtcNow - timeSpan;
        }
        
        public bool IsInProgress()
        {
            return Status == "PENDING" && StartedAt.HasValue && !CompletedAt.HasValue;
        }
        
        public bool IsStuck()
        {
            // Consider a log entry stuck if it's been pending for more than 30 minutes
            return IsInProgress() && (DateTime.UtcNow - StartedAt.Value).TotalMinutes > 30;
        }
    }
}