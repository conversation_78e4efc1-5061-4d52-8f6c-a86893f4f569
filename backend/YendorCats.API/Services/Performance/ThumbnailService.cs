using System.Diagnostics;
using Microsoft.Extensions.Caching.Memory;
using YendorCats.API.Data.Repositories;
using YendorCats.API.Models;
using YendorCats.API.Services.Storage;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;

namespace YendorCats.API.Services.Performance
{
    /// <summary>
    /// High-performance thumbnail generation service
    /// Optimizes image loading through pre-generated thumbnails stored in B2
    /// </summary>
    public class ThumbnailService : IThumbnailService
    {
        private readonly IGalleryRepository _galleryRepository;
        private readonly IS3StorageService _storageService;
        private readonly IMemoryCache _cache;
        private readonly ILogger<ThumbnailService> _logger;
        
        // Configuration
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(30);
        private const string CACHE_PREFIX = "thumbnail_";
        private const int JPEG_QUALITY = 85;
        private const int MAX_CONCURRENT_GENERATIONS = 5;
        
        // Semaphore for controlling concurrent thumbnail generation
        private readonly SemaphoreSlim _generationSemaphore;
        
        // Statistics tracking
        private static int _totalGenerations = 0;
        private static int _successfulGenerations = 0;
        private static int _failedGenerations = 0;
        private static readonly List<TimeSpan> _generationTimes = new();

        public ThumbnailService(
            IGalleryRepository galleryRepository,
            IS3StorageService storageService,
            IMemoryCache cache,
            ILogger<ThumbnailService> logger)
        {
            _galleryRepository = galleryRepository;
            _storageService = storageService;
            _cache = cache;
            _logger = logger;
            _generationSemaphore = new SemaphoreSlim(MAX_CONCURRENT_GENERATIONS, MAX_CONCURRENT_GENERATIONS);
        }

        public async Task<ThumbnailResult> GenerateThumbnailAsync(long imageId, ThumbnailSize size = ThumbnailSize.Medium)
        {
            var stopwatch = Stopwatch.StartNew();
            Interlocked.Increment(ref _totalGenerations);
            
            try
            {
                // Get image metadata from database
                var image = await _galleryRepository.GetByIdAsync(imageId);
                if (image == null)
                {
                    return new ThumbnailResult
                    {
                        Success = false,
                        ErrorMessage = $"Image with ID {imageId} not found",
                        GenerationTime = stopwatch.Elapsed
                    };
                }

                // Check if thumbnail already exists
                var existingThumbnailKey = GetThumbnailKey(image.StorageKey, size);
                var cacheKey = $"{CACHE_PREFIX}{imageId}_{size}";
                
                if (_cache.TryGetValue(cacheKey, out string? cachedUrl) && !string.IsNullOrEmpty(cachedUrl))
                {
                    return new ThumbnailResult
                    {
                        Success = true,
                        ThumbnailUrl = cachedUrl,
                        StorageKey = existingThumbnailKey,
                        Size = size,
                        GenerationTime = stopwatch.Elapsed
                    };
                }

                // Check if thumbnail exists in storage
                if (await _storageService.FileExistsAsync(existingThumbnailKey))
                {
                    var thumbnailUrl = await _storageService.GetPublicUrlAsync(existingThumbnailKey);
                    _cache.Set(cacheKey, thumbnailUrl, _cacheExpiry);
                    
                    return new ThumbnailResult
                    {
                        Success = true,
                        ThumbnailUrl = thumbnailUrl,
                        StorageKey = existingThumbnailKey,
                        Size = size,
                        GenerationTime = stopwatch.Elapsed
                    };
                }

                // Generate new thumbnail
                await _generationSemaphore.WaitAsync();
                try
                {
                    return await GenerateNewThumbnailAsync(image, size, stopwatch);
                }
                finally
                {
                    _generationSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                Interlocked.Increment(ref _failedGenerations);
                _logger.LogError(ex, "Failed to generate thumbnail for image {ImageId}, size {Size}", imageId, size);
                
                return new ThumbnailResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    Size = size,
                    GenerationTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<ThumbnailResult> GenerateThumbnailFromStreamAsync(Stream imageStream, string originalFileName, 
            ThumbnailSize size = ThumbnailSize.Medium)
        {
            var stopwatch = Stopwatch.StartNew();
            Interlocked.Increment(ref _totalGenerations);
            
            try
            {
                await _generationSemaphore.WaitAsync();
                try
                {
                    using var image = await Image.LoadAsync(imageStream);
                    var thumbnailStream = await ResizeImageAsync(image, size);
                    
                    // Generate storage key for thumbnail
                    var thumbnailKey = GetThumbnailKeyFromFileName(originalFileName, size);
                    
                    // Upload thumbnail to storage
                    var uploadResult = await _storageService.UploadFileAsync(thumbnailStream, thumbnailKey, "thumbnails");
                    
                    if (uploadResult.Success)
                    {
                        Interlocked.Increment(ref _successfulGenerations);
                        TrackGenerationTime(stopwatch.Elapsed);
                        
                        var thumbnailUrl = await _storageService.GetPublicUrlAsync(thumbnailKey);
                        
                        return new ThumbnailResult
                        {
                            Success = true,
                            ThumbnailUrl = thumbnailUrl,
                            StorageKey = thumbnailKey,
                            Size = size,
                            Width = GetTargetDimension(size),
                            Height = GetTargetDimension(size),
                            FileSize = thumbnailStream.Length,
                            GenerationTime = stopwatch.Elapsed
                        };
                    }
                    else
                    {
                        throw new Exception($"Failed to upload thumbnail: {uploadResult.ErrorMessage}");
                    }
                }
                finally
                {
                    _generationSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                Interlocked.Increment(ref _failedGenerations);
                _logger.LogError(ex, "Failed to generate thumbnail from stream for file {FileName}, size {Size}", originalFileName, size);
                
                return new ThumbnailResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    Size = size,
                    GenerationTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<List<ThumbnailResult>> GenerateMultipleThumbnailsAsync(long imageId, params ThumbnailSize[] sizes)
        {
            var tasks = sizes.Select(size => GenerateThumbnailAsync(imageId, size));
            var results = await Task.WhenAll(tasks);
            return results.ToList();
        }

        public async Task<BatchThumbnailResult> GenerateBatchThumbnailsAsync(IEnumerable<long> imageIds, 
            ThumbnailSize size = ThumbnailSize.Medium)
        {
            var result = new BatchThumbnailResult
            {
                StartTime = DateTime.UtcNow,
                TotalImages = imageIds.Count()
            };
            
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var tasks = imageIds.Select(async id =>
                {
                    try
                    {
                        var thumbnailResult = await GenerateThumbnailAsync(id, size);
                        lock (result)
                        {
                            result.Results.Add(thumbnailResult);
                            if (thumbnailResult.Success)
                                result.SuccessfulThumbnails++;
                            else
                            {
                                result.FailedThumbnails++;
                                result.Errors.Add($"Image {id}: {thumbnailResult.ErrorMessage}");
                            }
                        }
                        return thumbnailResult;
                    }
                    catch (Exception ex)
                    {
                        lock (result)
                        {
                            result.FailedThumbnails++;
                            result.Errors.Add($"Image {id}: {ex.Message}");
                        }
                        return new ThumbnailResult { Success = false, ErrorMessage = ex.Message };
                    }
                });

                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Batch thumbnail generation failed");
                result.Errors.Add($"Batch operation failed: {ex.Message}");
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
                result.TotalDuration = stopwatch.Elapsed;
            }

            return result;
        }

        public async Task<BatchThumbnailResult> GenerateMissingThumbnailsAsync(string? category = null, int batchSize = 50)
        {
            var result = new BatchThumbnailResult { StartTime = DateTime.UtcNow };
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                // Get images without thumbnails
                var images = await GetImagesWithoutThumbnailsAsync(category, batchSize);
                result.TotalImages = images.Count;
                
                if (images.Count == 0)
                {
                    result.EndTime = DateTime.UtcNow;
                    result.TotalDuration = stopwatch.Elapsed;
                    return result;
                }

                // Generate thumbnails in batches to avoid overwhelming the system
                var imageIds = images.Select(img => img.Id);
                return await GenerateBatchThumbnailsAsync(imageIds, ThumbnailSize.Medium);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate missing thumbnails for category {Category}", category);
                result.Errors.Add($"Operation failed: {ex.Message}");
                result.EndTime = DateTime.UtcNow;
                result.TotalDuration = stopwatch.Elapsed;
                return result;
            }
        }

        private async Task<ThumbnailResult> GenerateNewThumbnailAsync(CatGalleryImage image, ThumbnailSize size, Stopwatch stopwatch)
        {
            try
            {
                // Download original image
                var originalStream = await _storageService.DownloadFileAsync(image.StorageKey);
                if (originalStream == null)
                {
                    throw new Exception($"Could not download original image: {image.StorageKey}");
                }

                using (originalStream)
                using (var imageSharp = await Image.LoadAsync(originalStream))
                {
                    // Resize image
                    var thumbnailStream = await ResizeImageAsync(imageSharp, size);

                    // Generate storage key for thumbnail
                    var thumbnailKey = GetThumbnailKey(image.StorageKey, size);

                    // Upload thumbnail to storage
                    var uploadResult = await _storageService.UploadFileAsync(thumbnailStream, thumbnailKey, "thumbnails");

                    if (uploadResult.Success)
                    {
                        Interlocked.Increment(ref _successfulGenerations);
                        TrackGenerationTime(stopwatch.Elapsed);

                        var thumbnailUrl = await _storageService.GetPublicUrlAsync(thumbnailKey);

                        // Cache the URL
                        var cacheKey = $"{CACHE_PREFIX}{image.Id}_{size}";
                        _cache.Set(cacheKey, thumbnailUrl, _cacheExpiry);

                        // Update database with thumbnail reference if it's the medium size (default)
                        if (size == ThumbnailSize.Medium)
                        {
                            image.ThumbnailStorageKey = thumbnailKey;
                            await _galleryRepository.UpdateAsync(image);
                        }

                        return new ThumbnailResult
                        {
                            Success = true,
                            ThumbnailUrl = thumbnailUrl,
                            StorageKey = thumbnailKey,
                            Size = size,
                            Width = GetTargetDimension(size),
                            Height = GetTargetDimension(size),
                            FileSize = thumbnailStream.Length,
                            GenerationTime = stopwatch.Elapsed
                        };
                    }
                    else
                    {
                        throw new Exception($"Failed to upload thumbnail: {uploadResult.ErrorMessage}");
                    }
                }
            }
            catch (Exception ex)
            {
                Interlocked.Increment(ref _failedGenerations);
                throw new Exception($"Thumbnail generation failed: {ex.Message}", ex);
            }
        }

        private async Task<Stream> ResizeImageAsync(Image image, ThumbnailSize size)
        {
            var targetSize = GetTargetDimension(size);

            // Calculate new dimensions maintaining aspect ratio
            var (newWidth, newHeight) = CalculateNewDimensions(image.Width, image.Height, targetSize);

            // Resize image
            image.Mutate(x => x.Resize(new ResizeOptions
            {
                Size = new Size(newWidth, newHeight),
                Mode = ResizeMode.Max,
                Position = AnchorPositionMode.Center
            }));

            // Save to stream
            var outputStream = new MemoryStream();
            await image.SaveAsJpegAsync(outputStream, new JpegEncoder { Quality = JPEG_QUALITY });
            outputStream.Position = 0;

            return outputStream;
        }

        private (int width, int height) CalculateNewDimensions(int originalWidth, int originalHeight, int maxDimension)
        {
            if (originalWidth <= maxDimension && originalHeight <= maxDimension)
            {
                return (originalWidth, originalHeight);
            }

            var aspectRatio = (double)originalWidth / originalHeight;

            if (originalWidth > originalHeight)
            {
                return (maxDimension, (int)(maxDimension / aspectRatio));
            }
            else
            {
                return ((int)(maxDimension * aspectRatio), maxDimension);
            }
        }
        
        private string GetThumbnailKey(string originalKey, ThumbnailSize size)
        {
            var extension = Path.GetExtension(originalKey);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalKey);
            var directory = Path.GetDirectoryName(originalKey)?.Replace('\\', '/') ?? "";
            
            return $"{directory}/thumbnails/{nameWithoutExtension}_{size.ToString().ToLower()}{extension}";
        }
        
        private string GetThumbnailKeyFromFileName(string fileName, ThumbnailSize size)
        {
            var extension = Path.GetExtension(fileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            
            return $"thumbnails/{nameWithoutExtension}_{size.ToString().ToLower()}{extension}";
        }
        
        private int GetTargetDimension(ThumbnailSize size) => (int)size;
        
        private void TrackGenerationTime(TimeSpan duration)
        {
            lock (_generationTimes)
            {
                _generationTimes.Add(duration);
                if (_generationTimes.Count > 1000) // Keep only last 1000 entries
                {
                    _generationTimes.RemoveAt(0);
                }
            }
        }
        
        private async Task<List<CatGalleryImage>> GetImagesWithoutThumbnailsAsync(string? category, int limit)
        {
            // Get images that don't have thumbnail references
            var query = await _galleryRepository.GetImagesWithoutThumbnailsAsync(category, limit);
            return query.ToList();
        }

        public async Task<string?> GetThumbnailUrlAsync(long imageId, ThumbnailSize size = ThumbnailSize.Medium)
        {
            var cacheKey = $"{CACHE_PREFIX}{imageId}_{size}";

            if (_cache.TryGetValue(cacheKey, out string? cachedUrl) && !string.IsNullOrEmpty(cachedUrl))
            {
                return cachedUrl;
            }

            var image = await _galleryRepository.GetByIdAsync(imageId);
            if (image == null) return null;

            var thumbnailKey = GetThumbnailKey(image.StorageKey, size);

            if (await _storageService.FileExistsAsync(thumbnailKey))
            {
                var url = await _storageService.GetPublicUrlAsync(thumbnailKey);
                _cache.Set(cacheKey, url, _cacheExpiry);
                return url;
            }

            return null;
        }

        public async Task<bool> ThumbnailExistsAsync(long imageId, ThumbnailSize size = ThumbnailSize.Medium)
        {
            var image = await _galleryRepository.GetByIdAsync(imageId);
            if (image == null) return false;

            var thumbnailKey = GetThumbnailKey(image.StorageKey, size);
            return await _storageService.FileExistsAsync(thumbnailKey);
        }

        public async Task<bool> DeleteThumbnailAsync(long imageId, ThumbnailSize size = ThumbnailSize.Medium)
        {
            try
            {
                var image = await _galleryRepository.GetByIdAsync(imageId);
                if (image == null) return false;

                var thumbnailKey = GetThumbnailKey(image.StorageKey, size);
                var result = await _storageService.DeleteFileAsync(thumbnailKey);

                if (result.Success)
                {
                    // Remove from cache
                    var cacheKey = $"{CACHE_PREFIX}{imageId}_{size}";
                    _cache.Remove(cacheKey);

                    // Update database if it was the default thumbnail
                    if (size == ThumbnailSize.Medium && image.ThumbnailStorageKey == thumbnailKey)
                    {
                        image.ThumbnailStorageKey = null;
                        await _galleryRepository.UpdateAsync(image);
                    }
                }

                return result.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete thumbnail for image {ImageId}, size {Size}", imageId, size);
                return false;
            }
        }

        public async Task<bool> DeleteAllThumbnailsAsync(long imageId)
        {
            var sizes = Enum.GetValues<ThumbnailSize>();
            var tasks = sizes.Select(size => DeleteThumbnailAsync(imageId, size));
            var results = await Task.WhenAll(tasks);
            return results.All(r => r);
        }

        public async Task<CleanupResult> CleanupOrphanedThumbnailsAsync()
        {
            var result = new CleanupResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // This would require listing all thumbnail files and checking if parent images exist
                // Implementation would depend on storage service capabilities
                _logger.LogInformation("Starting orphaned thumbnail cleanup");

                // For now, return empty result
                result.Duration = stopwatch.Elapsed;
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cleanup orphaned thumbnails");
                result.Errors.Add(ex.Message);
                result.Duration = stopwatch.Elapsed;
                return result;
            }
        }

        public async Task<BatchThumbnailResult> RegenerateThumbnailsAsync(string? category = null, bool forceRegenerate = false)
        {
            var result = new BatchThumbnailResult { StartTime = DateTime.UtcNow };
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Get images for regeneration
                var images = forceRegenerate
                    ? await GetAllImagesAsync(category, 1000)
                    : await GetImagesWithoutThumbnailsAsync(category, 1000);

                result.TotalImages = images.Count;

                if (images.Count == 0)
                {
                    result.EndTime = DateTime.UtcNow;
                    result.TotalDuration = stopwatch.Elapsed;
                    return result;
                }

                var imageIds = images.Select(img => img.Id);
                return await GenerateBatchThumbnailsAsync(imageIds, ThumbnailSize.Medium);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to regenerate thumbnails for category {Category}", category);
                result.Errors.Add($"Operation failed: {ex.Message}");
                result.EndTime = DateTime.UtcNow;
                result.TotalDuration = stopwatch.Elapsed;
                return result;
            }
        }

        public async Task<ThumbnailStatistics> GetStatisticsAsync()
        {
            try
            {
                var stats = new ThumbnailStatistics();

                // Get basic counts from repository
                var totalImages = await _galleryRepository.GetTotalCountAsync();
                var imagesWithThumbnails = await _galleryRepository.GetImagesWithThumbnailsCountAsync();

                stats.TotalImages = totalImages;
                stats.ImagesWithThumbnails = imagesWithThumbnails;
                stats.ImagesWithoutThumbnails = totalImages - imagesWithThumbnails;

                // Calculate average generation time
                lock (_generationTimes)
                {
                    if (_generationTimes.Count > 0)
                    {
                        stats.AverageGenerationTime = TimeSpan.FromMilliseconds(
                            _generationTimes.Average(t => t.TotalMilliseconds));
                    }
                }

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get thumbnail statistics");
                return new ThumbnailStatistics();
            }
        }

        public async Task<ThumbnailHealthCheck> GetHealthCheckAsync()
        {
            var healthCheck = new ThumbnailHealthCheck();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Test storage accessibility
                var storageStopwatch = Stopwatch.StartNew();
                healthCheck.StorageAccessible = await _storageService.TestConnectionAsync();
                healthCheck.StorageResponseTime = storageStopwatch.Elapsed;

                // Test image processing
                var processingStopwatch = Stopwatch.StartNew();
                try
                {
                    using var testImage = new Image<SixLabors.ImageSharp.PixelFormats.Rgba32>(100, 100);
                    using var resized = await ResizeImageAsync(testImage, ThumbnailSize.Small);
                    healthCheck.ImageProcessingWorking = true;
                }
                catch
                {
                    healthCheck.ImageProcessingWorking = false;
                    healthCheck.Issues.Add("Image processing test failed");
                }
                healthCheck.ProcessingResponseTime = processingStopwatch.Elapsed;

                // Calculate health status
                healthCheck.IsHealthy = healthCheck.StorageAccessible && healthCheck.ImageProcessingWorking;
                healthCheck.Status = healthCheck.IsHealthy ? "Healthy" : "Unhealthy";

                // Add metrics
                healthCheck.Metrics["TotalGenerations"] = _totalGenerations;
                healthCheck.Metrics["SuccessfulGenerations"] = _successfulGenerations;
                healthCheck.Metrics["FailedGenerations"] = _failedGenerations;
                healthCheck.Metrics["SuccessRate"] = _totalGenerations > 0 ? (double)_successfulGenerations / _totalGenerations : 0;

                return healthCheck;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                healthCheck.IsHealthy = false;
                healthCheck.Status = "Error";
                healthCheck.Issues.Add($"Health check error: {ex.Message}");
                return healthCheck;
            }
        }

        private async Task<List<CatGalleryImage>> GetAllImagesAsync(string? category, int limit)
        {
            // Get all images for a category
            var result = await _galleryRepository.GetCategoryImagesAsync(category ?? "gallery", 1, limit, "DateUploaded", true);
            return result.Items.ToList();
        }
    }
}
