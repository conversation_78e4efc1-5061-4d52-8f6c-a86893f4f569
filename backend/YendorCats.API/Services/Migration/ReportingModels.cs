using System;
using System.Collections.Generic;
using YendorCats.API.Services.Migration;

namespace YendorCats.API.Services.Migration
{
    /// <summary>
    /// Migration dashboard data with real-time metrics
    /// </summary>
    public class MigrationDashboard
    {
        /// <summary>
        /// Dashboard generation timestamp
        /// </summary>
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// Overall migration statistics
        /// </summary>
        public MigrationStatistics Statistics { get; set; } = new();

        /// <summary>
        /// Currently active migrations
        /// </summary>
        public List<MigrationStatus> ActiveMigrations { get; set; } = new();

        /// <summary>
        /// Recent migration history
        /// </summary>
        public List<MigrationStatus> RecentMigrations { get; set; } = new();

        /// <summary>
        /// Total migrations count
        /// </summary>
        public int TotalMigrations { get; set; }

        /// <summary>
        /// Running migrations count
        /// </summary>
        public int RunningMigrations { get; set; }

        /// <summary>
        /// Completed migrations count
        /// </summary>
        public int CompletedMigrations { get; set; }

        /// <summary>
        /// Failed migrations count
        /// </summary>
        public int FailedMigrations { get; set; }

        /// <summary>
        /// Overall success rate percentage
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// System health metrics
        /// </summary>
        public SystemHealthMetrics SystemHealth { get; set; } = new();

        /// <summary>
        /// Performance metrics
        /// </summary>
        public PerformanceMetrics PerformanceMetrics { get; set; } = new();

        /// <summary>
        /// Error summary
        /// </summary>
        public ErrorSummary ErrorSummary { get; set; } = new();

        /// <summary>
        /// Error message if dashboard generation failed
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Detailed migration report with comprehensive analysis
    /// </summary>
    public class DetailedMigrationReport
    {
        /// <summary>
        /// Migration ID
        /// </summary>
        public string MigrationId { get; set; } = string.Empty;

        /// <summary>
        /// Report generation timestamp
        /// </summary>
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// Whether validation results are included
        /// </summary>
        public bool IncludesValidation { get; set; }

        /// <summary>
        /// Migration status information
        /// </summary>
        public MigrationStatus? MigrationStatus { get; set; }

        /// <summary>
        /// Migration results (if completed)
        /// </summary>
        public MigrationResult? MigrationResult { get; set; }

        /// <summary>
        /// Real-time migration progress
        /// </summary>
        public MigrationProgress? MigrationProgress { get; set; }

        /// <summary>
        /// Migration logs
        /// </summary>
        public MigrationLogs? MigrationLogs { get; set; }

        /// <summary>
        /// Validation results (if requested)
        /// </summary>
        public ComprehensiveValidationResult? ValidationResult { get; set; }

        /// <summary>
        /// Performance analysis
        /// </summary>
        public PerformanceAnalysis? PerformanceAnalysis { get; set; }

        /// <summary>
        /// Recommendations for improvement
        /// </summary>
        public List<string> Recommendations { get; set; } = new();

        /// <summary>
        /// Error message if report generation failed
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Executive summary report for management
    /// </summary>
    public class ExecutiveSummaryReport
    {
        /// <summary>
        /// Report period start date
        /// </summary>
        public DateTime ReportPeriodStart { get; set; }

        /// <summary>
        /// Report period end date
        /// </summary>
        public DateTime ReportPeriodEnd { get; set; }

        /// <summary>
        /// Report generation timestamp
        /// </summary>
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// Total migrations in period
        /// </summary>
        public int TotalMigrations { get; set; }

        /// <summary>
        /// Successful migrations in period
        /// </summary>
        public int SuccessfulMigrations { get; set; }

        /// <summary>
        /// Failed migrations in period
        /// </summary>
        public int FailedMigrations { get; set; }

        /// <summary>
        /// Success rate percentage
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// Total data processed
        /// </summary>
        public long TotalDataProcessed { get; set; }

        /// <summary>
        /// Total data size in bytes
        /// </summary>
        public long TotalDataSizeBytes { get; set; }

        /// <summary>
        /// Average processing time in minutes
        /// </summary>
        public double AverageProcessingTime { get; set; }

        /// <summary>
        /// Total processing time in minutes
        /// </summary>
        public double TotalProcessingTime { get; set; }

        /// <summary>
        /// Key achievements during the period
        /// </summary>
        public List<string> KeyAchievements { get; set; } = new();

        /// <summary>
        /// Challenges and issues encountered
        /// </summary>
        public List<string> ChallengesAndIssues { get; set; } = new();

        /// <summary>
        /// Executive recommendations
        /// </summary>
        public List<string> Recommendations { get; set; } = new();

        /// <summary>
        /// Error message if report generation failed
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Real-time metrics for monitoring
    /// </summary>
    public class RealTimeMetrics
    {
        /// <summary>
        /// Metrics timestamp
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Currently active migrations
        /// </summary>
        public int ActiveMigrations { get; set; }

        /// <summary>
        /// Queued migrations waiting to start
        /// </summary>
        public int QueuedMigrations { get; set; }

        /// <summary>
        /// Migrations completed today
        /// </summary>
        public int CompletedToday { get; set; }

        /// <summary>
        /// Migrations failed today
        /// </summary>
        public int FailedToday { get; set; }

        /// <summary>
        /// Current throughput (items per second)
        /// </summary>
        public double CurrentThroughput { get; set; }

        /// <summary>
        /// System performance metrics
        /// </summary>
        public SystemPerformanceMetrics SystemPerformance { get; set; } = new();

        /// <summary>
        /// Error message if metrics collection failed
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Trending analysis over time
    /// </summary>
    public class TrendingAnalysis
    {
        /// <summary>
        /// Analysis period in days
        /// </summary>
        public int AnalysisPeriodDays { get; set; }

        /// <summary>
        /// Analysis generation timestamp
        /// </summary>
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// Daily migration trends
        /// </summary>
        public List<DailyTrend> DailyTrends { get; set; } = new();

        /// <summary>
        /// Performance trends over time
        /// </summary>
        public List<PerformanceTrend> PerformanceTrends { get; set; } = new();

        /// <summary>
        /// Error trends over time
        /// </summary>
        public List<ErrorTrend> ErrorTrends { get; set; } = new();

        /// <summary>
        /// Error message if analysis failed
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// System health metrics
    /// </summary>
    public class SystemHealthMetrics
    {
        /// <summary>
        /// Database health status
        /// </summary>
        public string DatabaseHealth { get; set; } = string.Empty;

        /// <summary>
        /// S3 storage health status
        /// </summary>
        public string S3Health { get; set; } = string.Empty;

        /// <summary>
        /// B2 storage health status
        /// </summary>
        public string B2Health { get; set; } = string.Empty;

        /// <summary>
        /// System load (0.0 - 1.0)
        /// </summary>
        public double SystemLoad { get; set; }

        /// <summary>
        /// Memory usage (0.0 - 1.0)
        /// </summary>
        public double MemoryUsage { get; set; }

        /// <summary>
        /// Disk usage (0.0 - 1.0)
        /// </summary>
        public double DiskUsage { get; set; }

        /// <summary>
        /// Last health check timestamp
        /// </summary>
        public DateTime LastHealthCheck { get; set; }
    }

    /// <summary>
    /// Performance metrics
    /// </summary>
    public class PerformanceMetrics
    {
        /// <summary>
        /// Average response time in milliseconds
        /// </summary>
        public double AverageResponseTime { get; set; }

        /// <summary>
        /// Throughput per second
        /// </summary>
        public double ThroughputPerSecond { get; set; }

        /// <summary>
        /// Error rate (0.0 - 1.0)
        /// </summary>
        public double ErrorRate { get; set; }

        /// <summary>
        /// Cache hit rate (0.0 - 1.0)
        /// </summary>
        public double CacheHitRate { get; set; }

        /// <summary>
        /// Database connection pool usage (0.0 - 1.0)
        /// </summary>
        public double DatabaseConnectionPool { get; set; }

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Error summary
    /// </summary>
    public class ErrorSummary
    {
        /// <summary>
        /// Total errors count
        /// </summary>
        public int TotalErrors { get; set; }

        /// <summary>
        /// Critical errors count
        /// </summary>
        public int CriticalErrors { get; set; }

        /// <summary>
        /// Warning errors count
        /// </summary>
        public int WarningErrors { get; set; }

        /// <summary>
        /// Info errors count
        /// </summary>
        public int InfoErrors { get; set; }

        /// <summary>
        /// Most common error types and their counts
        /// </summary>
        public Dictionary<string, int> MostCommonErrors { get; set; } = new();

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Performance analysis for a specific migration
    /// </summary>
    public class PerformanceAnalysis
    {
        /// <summary>
        /// Migration ID
        /// </summary>
        public string MigrationId { get; set; } = string.Empty;

        /// <summary>
        /// Throughput analysis
        /// </summary>
        public string ThroughputAnalysis { get; set; } = string.Empty;

        /// <summary>
        /// Bottleneck analysis
        /// </summary>
        public string BottleneckAnalysis { get; set; } = string.Empty;

        /// <summary>
        /// Optimization suggestions
        /// </summary>
        public List<string> OptimizationSuggestions { get; set; } = new();

        /// <summary>
        /// Performance score (0-100)
        /// </summary>
        public int PerformanceScore { get; set; }

        /// <summary>
        /// Analysis generation timestamp
        /// </summary>
        public DateTime GeneratedAt { get; set; }
    }

    /// <summary>
    /// Daily migration trend
    /// </summary>
    public class DailyTrend
    {
        /// <summary>
        /// Date of the trend
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Total migrations for the day
        /// </summary>
        public int TotalMigrations { get; set; }

        /// <summary>
        /// Successful migrations for the day
        /// </summary>
        public int SuccessfulMigrations { get; set; }

        /// <summary>
        /// Failed migrations for the day
        /// </summary>
        public int FailedMigrations { get; set; }

        /// <summary>
        /// Average processing time for the day
        /// </summary>
        public double AverageProcessingTime { get; set; }

        /// <summary>
        /// Success rate for the day
        /// </summary>
        public double SuccessRate => TotalMigrations > 0 ? (double)SuccessfulMigrations / TotalMigrations * 100 : 0;
    }

    /// <summary>
    /// Performance trend over time
    /// </summary>
    public class PerformanceTrend
    {
        /// <summary>
        /// Date of the trend
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Average processing time in minutes
        /// </summary>
        public double AverageProcessingTime { get; set; }

        /// <summary>
        /// Throughput per minute
        /// </summary>
        public double ThroughputPerMinute { get; set; }

        /// <summary>
        /// Error rate percentage
        /// </summary>
        public double ErrorRate { get; set; }
    }

    /// <summary>
    /// Error trend over time
    /// </summary>
    public class ErrorTrend
    {
        /// <summary>
        /// Date of the trend
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Total errors for the day
        /// </summary>
        public int TotalErrors { get; set; }

        /// <summary>
        /// Critical errors for the day
        /// </summary>
        public int CriticalErrors { get; set; }

        /// <summary>
        /// Warning errors for the day
        /// </summary>
        public int WarningErrors { get; set; }

        /// <summary>
        /// Error rate percentage
        /// </summary>
        public double ErrorRate { get; set; }
    }

    /// <summary>
    /// System performance metrics
    /// </summary>
    public class SystemPerformanceMetrics
    {
        /// <summary>
        /// CPU usage percentage (0.0 - 1.0)
        /// </summary>
        public double CpuUsage { get; set; }

        /// <summary>
        /// Memory usage percentage (0.0 - 1.0)
        /// </summary>
        public double MemoryUsage { get; set; }

        /// <summary>
        /// Disk usage percentage (0.0 - 1.0)
        /// </summary>
        public double DiskUsage { get; set; }

        /// <summary>
        /// Network usage percentage (0.0 - 1.0)
        /// </summary>
        public double NetworkUsage { get; set; }

        /// <summary>
        /// Active database connections
        /// </summary>
        public int DatabaseConnections { get; set; }

        /// <summary>
        /// Active threads count
        /// </summary>
        public int ActiveThreads { get; set; }

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Export formats for migration data
    /// </summary>
    public enum ExportFormat
    {
        /// <summary>
        /// JSON format
        /// </summary>
        JSON,

        /// <summary>
        /// CSV format
        /// </summary>
        CSV,

        /// <summary>
        /// XML format
        /// </summary>
        XML
    }

    /// <summary>
    /// Report generation options
    /// </summary>
    public class ReportOptions
    {
        /// <summary>
        /// Include detailed validation results
        /// </summary>
        public bool IncludeValidation { get; set; } = true;

        /// <summary>
        /// Include performance analysis
        /// </summary>
        public bool IncludePerformanceAnalysis { get; set; } = true;

        /// <summary>
        /// Include error details
        /// </summary>
        public bool IncludeErrorDetails { get; set; } = true;

        /// <summary>
        /// Include migration logs
        /// </summary>
        public bool IncludeLogs { get; set; } = false;

        /// <summary>
        /// Maximum number of log entries to include
        /// </summary>
        public int MaxLogEntries { get; set; } = 1000;

        /// <summary>
        /// Report format
        /// </summary>
        public ExportFormat Format { get; set; } = ExportFormat.JSON;

        /// <summary>
        /// Report time zone
        /// </summary>
        public string TimeZone { get; set; } = "UTC";
    }

    /// <summary>
    /// Migration health check result
    /// </summary>
    public class MigrationHealthCheck
    {
        /// <summary>
        /// Health check timestamp
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Overall health status
        /// </summary>
        public HealthStatus OverallStatus { get; set; }

        /// <summary>
        /// Database health status
        /// </summary>
        public HealthStatus DatabaseStatus { get; set; }

        /// <summary>
        /// S3 storage health status
        /// </summary>
        public HealthStatus S3Status { get; set; }

        /// <summary>
        /// B2 storage health status
        /// </summary>
        public HealthStatus B2Status { get; set; }

        /// <summary>
        /// Migration service health status
        /// </summary>
        public HealthStatus MigrationServiceStatus { get; set; }

        /// <summary>
        /// Health check details
        /// </summary>
        public List<HealthCheckDetail> Details { get; set; } = new();

        /// <summary>
        /// Health check recommendations
        /// </summary>
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// Health status enumeration
    /// </summary>
    public enum HealthStatus
    {
        /// <summary>
        /// System is healthy
        /// </summary>
        Healthy,

        /// <summary>
        /// System has warnings but is functional
        /// </summary>
        Warning,

        /// <summary>
        /// System has errors but is partially functional
        /// </summary>
        Error,

        /// <summary>
        /// System is critical and may not function properly
        /// </summary>
        Critical,

        /// <summary>
        /// System status is unknown
        /// </summary>
        Unknown
    }

    /// <summary>
    /// Health check detail
    /// </summary>
    public class HealthCheckDetail
    {
        /// <summary>
        /// Component name
        /// </summary>
        public string Component { get; set; } = string.Empty;

        /// <summary>
        /// Health status
        /// </summary>
        public HealthStatus Status { get; set; }

        /// <summary>
        /// Status message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Response time in milliseconds
        /// </summary>
        public long ResponseTimeMs { get; set; }

        /// <summary>
        /// Additional details
        /// </summary>
        public Dictionary<string, object> Details { get; set; } = new();
    }
}