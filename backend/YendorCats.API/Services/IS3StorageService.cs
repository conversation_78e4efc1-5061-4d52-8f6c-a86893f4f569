using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Interface for S3 storage operations
    /// </summary>
    public interface IS3StorageService
    {
        /// <summary>
        /// Upload a file to S3
        /// </summary>
        /// <param name="fileStream">The file stream to upload</param>
        /// <param name="fileName">The name of the file</param>
        /// <param name="contentType">The content type of the file</param>
        /// <returns>The URL of the uploaded file</returns>
        Task<string> UploadFileAsync(Stream fileStream, string fileName, string contentType);

        /// <summary>
        /// Upload a file to S3 with metadata
        /// </summary>
        /// <param name="fileStream">The file stream to upload</param>
        /// <param name="fileName">The name of the file</param>
        /// <param name="contentType">The content type of the file</param>
        /// <param name="metadata">The metadata to associate with the file</param>
        /// <returns>The URL of the uploaded file</returns>
        Task<string> UploadFileWithMetadataAsync(Stream fileStream, string fileName, string contentType, Dictionary<string, string> metadata);

        /// <summary>
        /// Delete a file from S3
        /// </summary>
        /// <param name="fileName">The name of the file to delete</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task DeleteFileAsync(string fileName);

        /// <summary>
        /// Get a pre-signed URL for a file
        /// </summary>
        /// <param name="fileName">The name of the file</param>
        /// <param name="expiryMinutes">The number of minutes the URL is valid for</param>
        /// <returns>The pre-signed URL</returns>
        Task<string> GetPreSignedUrlAsync(string fileName, int expiryMinutes = 60);

        /// <summary>
        /// Get metadata for a file
        /// </summary>
        /// <param name="fileName">The name of the file</param>
        /// <returns>Dictionary containing the file's metadata</returns>
        Task<Dictionary<string, string>> GetObjectMetadataAsync(string fileName);

        /// <summary>
        /// Configure CORS for the S3 bucket
        /// </summary>
        /// <returns>Task representing the asynchronous operation</returns>
        Task ConfigureCorsAsync();

        /// <summary>
        /// List files in S3 with a specific prefix
        /// </summary>
        /// <param name="prefix">The prefix to filter by</param>
        /// <returns>A list of S3 objects</returns>
        Task<List<Amazon.S3.Model.S3Object>> ListFilesAsync(string prefix);
        // TODO: Review merge conflict - resolve conflicting implementations
        // <<<<<<< HEAD
        // =======

        /// <summary>
        /// Update metadata for an existing S3 object
        /// </summary>
        /// <param name="fileName">The name of the file</param>
        /// <param name="metadata">The new metadata to apply</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task UpdateObjectMetadataAsync(string fileName, Dictionary<string, string> metadata);

        /// <summary>
        /// Get S3 configuration information for admin access
        /// </summary>
        /// <returns>S3 configuration details</returns>
        Task<Dictionary<string, object>> GetS3ConfigurationAsync();

        /// <summary>
        /// Search objects by metadata criteria
        /// </summary>
        /// <param name="metadataFilters">Metadata key-value pairs to filter by</param>
        /// <param name="prefix">Optional prefix to limit search scope</param>
        /// <returns>List of matching S3 objects with metadata</returns>
        Task<List<S3ObjectWithMetadata>> SearchByMetadataAsync(Dictionary<string, string> metadataFilters, string prefix = "");
    }

    /// <summary>
    /// S3 object with associated metadata
    /// </summary>
    public class S3ObjectWithMetadata
    {
        public Amazon.S3.Model.S3Object S3Object { get; set; } = new();
        public Dictionary<string, string> Metadata { get; set; } = new();
        public string PublicUrl { get; set; } = string.Empty;
        // >>>>>>> dcb0913 (cat profile implementation. project dockerisation and docker container debugging)
    }
}
